'use client';

import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import Image from "next/image";
import Link from 'next/link';

// 简化的翻译hook
function useTranslation() {
  const router = useRouter();
  const [locale, setLocale] = useState('zh');
  const [t, setT] = useState({});

  useEffect(() => {
    // 从URL或localStorage获取语言设置
    const savedLocale = localStorage.getItem('locale') || 'zh';
    setLocale(savedLocale);
    loadTranslations(savedLocale);
  }, []);

  const loadTranslations = async (lang: string) => {
    try {
      const response = await fetch(`/locales/${lang}/common.json`);
      const translations = await response.json();
      setT(translations);
    } catch (error) {
      console.error('Failed to load translations:', error);
    }
  };

  const changeLanguage = (newLocale: string) => {
    setLocale(newLocale);
    localStorage.setItem('locale', newLocale);
    loadTranslations(newLocale);
  };

  return { t, locale, changeLanguage };
}

// 语言切换组件
function LanguageSwitcher({ currentLocale, onLanguageChange }: { currentLocale: string, onLanguageChange: (locale: string) => void }) {
  const languages = [
    { code: 'zh', name: '中文', flag: '🇨🇳' },
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'ru', name: 'Русский', flag: '🇷🇺' }
  ];

  return (
    <div className="flex gap-2">
      {languages.map((lang) => (
        <button
          key={lang.code}
          onClick={() => onLanguageChange(lang.code)}
          className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
            currentLocale === lang.code
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          {lang.flag} {lang.name}
        </button>
      ))}
    </div>
  );
}

// 导航组件
function Navigation({ t, locale, onLanguageChange }: any) {
  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link href="/" className="text-xl font-bold text-blue-600">
              KARN Materials
            </Link>
          </div>

          <div className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-gray-700 hover:text-blue-600 font-medium">
              {t.nav?.home || '首页'}
            </Link>
            <Link href="/products" className="text-gray-700 hover:text-blue-600 font-medium">
              {t.nav?.products || '产品中心'}
            </Link>
            <Link href="/about" className="text-gray-700 hover:text-blue-600 font-medium">
              {t.nav?.about || '关于我们'}
            </Link>
            <Link href="/contact" className="text-gray-700 hover:text-blue-600 font-medium">
              {t.nav?.contact || '联系我们'}
            </Link>
            <Link href="/quote" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 font-medium">
              {t.nav?.quote || '在线询价'}
            </Link>
          </div>

          <LanguageSwitcher currentLocale={locale} onLanguageChange={onLanguageChange} />
        </div>
      </div>
    </nav>
  );
}

export default function Home() {
  const { t, locale, changeLanguage } = useTranslation();

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation t={t} locale={locale} onLanguageChange={changeLanguage} />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              {t.hero?.title || '专业墙纸胶粉制造商'}
            </h1>
            <p className="text-xl md:text-2xl mb-4 text-blue-100">
              {t.hero?.subtitle || '大包装原料 • 代加工服务 • 小包装半成品'}
            </p>
            <p className="text-lg mb-8 text-blue-100 max-w-3xl mx-auto">
              {t.hero?.description || '15年专注环保建材，服务50+国家，提供一站式解决方案'}
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/quote" className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                {t.hero?.cta?.quote || '立即询价'}
              </Link>
              <Link href="/products" className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                {t.hero?.cta?.brochure || '下载产品手册'}
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">15+</div>
              <div className="text-gray-600">{t.hero?.stats?.experience || '年专业经验'}</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">50+</div>
              <div className="text-gray-600">{t.hero?.stats?.countries || '出口国家'}</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">10000+</div>
              <div className="text-gray-600">{t.hero?.stats?.capacity || '吨年产能'}</div>
            </div>
          </div>
        </div>
      </section>

      {/* Business Segments */}
      <section className="bg-gray-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t.business?.title || '三大业务板块'}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Bulk Materials */}
            <div className="bg-white rounded-lg shadow-lg p-8 hover:shadow-xl transition-shadow">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">📦</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {t.business?.bulk?.title || '大包装原料'}
                </h3>
                <p className="text-gray-600">
                  {t.business?.bulk?.description || '20kg/袋专业装，适合批发商和工程项目'}
                </p>
              </div>

              <ul className="space-y-2 mb-6">
                {(t.business?.bulk?.features || [
                  'KG标准型 - 性价比首选',
                  '999强力型 - 厚重墙纸专用',
                  '999+超强型 - 防霉潮湿环境'
                ]).map((feature: string, index: number) => (
                  <li key={index} className="flex items-center text-sm text-gray-600">
                    <span className="text-green-500 mr-2">✓</span>
                    {feature}
                  </li>
                ))}
              </ul>

              <div className="border-t pt-4">
                <div className="text-sm text-gray-500">
                  <div>{t.business?.bulk?.specs?.ratio || '兑水比例: 1:25'}</div>
                  <div>{t.business?.bulk?.specs?.coverage || '覆盖面积: 200g约4卷'}</div>
                </div>
              </div>
            </div>

            {/* OEM Services */}
            <div className="bg-white rounded-lg shadow-lg p-8 hover:shadow-xl transition-shadow">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🏭</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {t.business?.oem?.title || '代加工服务'}
                </h3>
                <p className="text-gray-600">
                  {t.business?.oem?.description || 'OEM/ODM定制生产，助力您的品牌发展'}
                </p>
              </div>

              <ul className="space-y-2 mb-6">
                {(t.business?.oem?.features || [
                  '客户品牌定制生产',
                  '包装设计和印刷',
                  '质量标准严格把控'
                ]).map((feature: string, index: number) => (
                  <li key={index} className="flex items-center text-sm text-gray-600">
                    <span className="text-green-500 mr-2">✓</span>
                    {feature}
                  </li>
                ))}
              </ul>

              <div className="border-t pt-4">
                <div className="text-sm text-gray-500">
                  <div>{t.business?.oem?.specs?.moq || '最小起订: 1吨起'}</div>
                  <div>{t.business?.oem?.specs?.delivery || '交期: 15-30天'}</div>
                </div>
              </div>
            </div>

            {/* Retail Packages */}
            <div className="bg-white rounded-lg shadow-lg p-8 hover:shadow-xl transition-shadow">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🏠</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {t.business?.retail?.title || '小包装半成品'}
                </h3>
                <p className="text-gray-600">
                  {t.business?.retail?.description || '200g家用装，适合零售市场和终端消费者'}
                </p>
              </div>

              <ul className="space-y-2 mb-6">
                {(t.business?.retail?.features || [
                  '便民小包装设计',
                  '覆盖约4卷墙纸',
                  '使用说明详细'
                ]).map((feature: string, index: number) => (
                  <li key={index} className="flex items-center text-sm text-gray-600">
                    <span className="text-green-500 mr-2">✓</span>
                    {feature}
                  </li>
                ))}
              </ul>

              <div className="border-t pt-4">
                <div className="text-sm text-gray-500">
                  <div>{t.business?.retail?.specs?.package || '包装规格: 200g/袋'}</div>
                  <div>{t.business?.retail?.specs?.usage || '适用: 家庭装修'}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            {locale === 'zh' ? '准备开始合作？' : locale === 'en' ? 'Ready to Start Partnership?' : 'Готовы начать сотрудничество?'}
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            {locale === 'zh' ? '立即联系我们，获取专业的产品方案和报价' :
             locale === 'en' ? 'Contact us now for professional product solutions and quotes' :
             'Свяжитесь с нами сейчас для получения профессиональных решений и предложений'}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/quote" className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              {t.hero?.cta?.quote || '立即询价'}
            </Link>
            <Link href="/contact" className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
              {t.nav?.contact || '联系我们'}
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-xl font-bold mb-4">
                {t.company?.name || '杭州卡恩新型建材有限公司'}
              </h3>
              <p className="text-gray-400 mb-4">
                {t.footer?.slogan || '用品质说话'}
              </p>
              <div className="space-y-2 text-sm text-gray-400">
                <div>{t.company?.contact?.person || '聂磊 (Reggie Nie)'}</div>
                <div>{t.company?.contact?.phone || '+86 132 1615 6841'}</div>
                <div>{t.company?.contact?.email || '<EMAIL>'}</div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-4">{t.nav?.products || '产品中心'}</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><Link href="/products" className="hover:text-white">{t.business?.bulk?.title || '大包装原料'}</Link></li>
                <li><Link href="/products" className="hover:text-white">{t.business?.oem?.title || '代加工服务'}</Link></li>
                <li><Link href="/products" className="hover:text-white">{t.business?.retail?.title || '小包装半成品'}</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">{locale === 'zh' ? '快速链接' : locale === 'en' ? 'Quick Links' : 'Быстрые ссылки'}</h4>
              <ul className="space-y-2 text-sm text-gray-400">
                <li><Link href="/about" className="hover:text-white">{t.nav?.about || '关于我们'}</Link></li>
                <li><Link href="/contact" className="hover:text-white">{t.nav?.contact || '联系我们'}</Link></li>
                <li><Link href="/quote" className="hover:text-white">{t.nav?.quote || '在线询价'}</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
            {t.footer?.copyright || '© 2024 杭州卡恩新型建材有限公司. 保留所有权利.'}
          </div>
        </div>
      </footer>
    </div>
  );
}
