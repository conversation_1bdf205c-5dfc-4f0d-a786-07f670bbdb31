'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

// 简化的翻译hook（复用）
function useTranslation() {
  const [locale, setLocale] = useState('zh');
  const [t, setT] = useState({});

  useEffect(() => {
    const savedLocale = localStorage.getItem('locale') || 'zh';
    setLocale(savedLocale);
    loadTranslations(savedLocale);
  }, []);

  const loadTranslations = async (lang: string) => {
    try {
      const response = await fetch(`/locales/${lang}/common.json`);
      const translations = await response.json();
      setT(translations);
    } catch (error) {
      console.error('Failed to load translations:', error);
    }
  };

  return { t, locale };
}

// 询价表单组件
export default function QuotePage() {
  const { t, locale } = useTranslation();
  const [formData, setFormData] = useState({
    businessType: '',
    company: '',
    contactPerson: '',
    email: '',
    phone: '',
    country: '',
    city: '',
    productRequirements: {
      bulkMaterials: false,
      oemServices: false,
      retailPackages: false,
      quantity: '',
      specifications: ''
    },
    deliveryTerms: '',
    destinationPort: '',
    deliveryTime: '',
    paymentTerms: '',
    requirements: '',
    budgetRange: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [submitMessage, setSubmitMessage] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (name.startsWith('productRequirements.')) {
      const field = name.split('.')[1];
      if (type === 'checkbox') {
        setFormData(prev => ({
          ...prev,
          productRequirements: {
            ...prev.productRequirements,
            [field]: (e.target as HTMLInputElement).checked
          }
        }));
      } else {
        setFormData(prev => ({
          ...prev,
          productRequirements: {
            ...prev.productRequirements,
            [field]: value
          }
        }));
      }
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      const response = await fetch('/api/quotes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          language: locale,
          sourcePage: 'quote-form'
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setSubmitStatus('success');
        setSubmitMessage(
          locale === 'zh' ? `询价提交成功！询价编号：${result.quoteId}` :
          locale === 'en' ? `Quote submitted successfully! Quote ID: ${result.quoteId}` :
          `Запрос успешно отправлен! ID запроса: ${result.quoteId}`
        );
        // 重置表单
        setFormData({
          businessType: '',
          company: '',
          contactPerson: '',
          email: '',
          phone: '',
          country: '',
          city: '',
          productRequirements: {
            bulkMaterials: false,
            oemServices: false,
            retailPackages: false,
            quantity: '',
            specifications: ''
          },
          deliveryTerms: '',
          destinationPort: '',
          deliveryTime: '',
          paymentTerms: '',
          requirements: '',
          budgetRange: ''
        });
      } else {
        setSubmitStatus('error');
        setSubmitMessage(result.error || 'Submission failed');
      }
    } catch (error) {
      setSubmitStatus('error');
      setSubmitMessage(
        locale === 'zh' ? '提交失败，请稍后重试' :
        locale === 'en' ? 'Submission failed, please try again later' :
        'Ошибка отправки, попробуйте позже'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const getLabel = (key: string, fallback: string) => {
    return locale === 'zh' ? fallback :
           locale === 'en' ? key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()) :
           fallback; // 俄语标签需要单独翻译
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="text-xl font-bold text-blue-600">
              KARN Materials
            </Link>
            <Link href="/" className="text-gray-600 hover:text-blue-600">
              {locale === 'zh' ? '返回首页' : locale === 'en' ? 'Back to Home' : 'Вернуться на главную'}
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              {locale === 'zh' ? '在线询价' : locale === 'en' ? 'Request Quote' : 'Запрос цены'}
            </h1>
            <p className="text-gray-600">
              {locale === 'zh' ? '请填写以下信息，我们将在24小时内回复您的询价' :
               locale === 'en' ? 'Please fill out the information below, we will respond to your inquiry within 24 hours' :
               'Пожалуйста, заполните информацию ниже, мы ответим на ваш запрос в течение 24 часов'}
            </p>
          </div>

          {submitStatus === 'success' && (
            <div className="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-md">
              {submitMessage}
            </div>
          )}

          {submitStatus === 'error' && (
            <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
              {submitMessage}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Business Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {getLabel('businessType', '业务类型')} *
              </label>
              <select
                name="businessType"
                value={formData.businessType}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">
                  {locale === 'zh' ? '请选择业务类型' : locale === 'en' ? 'Select business type' : 'Выберите тип бизнеса'}
                </option>
                <option value="bulk">{t.business?.bulk?.title || '大包装原料'}</option>
                <option value="oem">{t.business?.oem?.title || '代加工服务'}</option>
                <option value="retail">{t.business?.retail?.title || '小包装半成品'}</option>
                <option value="mixed">
                  {locale === 'zh' ? '混合业务' : locale === 'en' ? 'Mixed Business' : 'Смешанный бизнес'}
                </option>
              </select>
            </div>

            {/* Company Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getLabel('company', '公司名称')} *
                </label>
                <input
                  type="text"
                  name="company"
                  value={formData.company}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getLabel('contactPerson', '联系人')} *
                </label>
                <input
                  type="text"
                  name="contactPerson"
                  value={formData.contactPerson}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getLabel('email', '邮箱')} *
                </label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getLabel('phone', '电话')} *
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getLabel('country', '国家')} *
                </label>
                <input
                  type="text"
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {getLabel('city', '城市')}
                </label>
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Product Requirements */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-4">
                {locale === 'zh' ? '产品需求' : locale === 'en' ? 'Product Requirements' : 'Требования к продукту'}
              </label>
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="productRequirements.bulkMaterials"
                    checked={formData.productRequirements.bulkMaterials}
                    onChange={handleInputChange}
                    className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">{t.business?.bulk?.title || '大包装原料'}</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="productRequirements.oemServices"
                    checked={formData.productRequirements.oemServices}
                    onChange={handleInputChange}
                    className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">{t.business?.oem?.title || '代加工服务'}</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="productRequirements.retailPackages"
                    checked={formData.productRequirements.retailPackages}
                    onChange={handleInputChange}
                    className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">{t.business?.retail?.title || '小包装半成品'}</span>
                </label>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {locale === 'zh' ? '预计数量' : locale === 'en' ? 'Estimated Quantity' : 'Ожидаемое количество'}
                </label>
                <input
                  type="text"
                  name="productRequirements.quantity"
                  value={formData.productRequirements.quantity}
                  onChange={handleInputChange}
                  placeholder={locale === 'zh' ? '例如：10吨/月' : locale === 'en' ? 'e.g., 10 tons/month' : 'например: 10 тонн/месяц'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {locale === 'zh' ? '预算范围' : locale === 'en' ? 'Budget Range' : 'Бюджетный диапазон'}
                </label>
                <select
                  name="budgetRange"
                  value={formData.budgetRange}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">
                    {locale === 'zh' ? '请选择预算范围' : locale === 'en' ? 'Select budget range' : 'Выберите бюджетный диапазон'}
                  </option>
                  <option value="under-10k">< $10,000</option>
                  <option value="10k-50k">$10,000 - $50,000</option>
                  <option value="50k-100k">$50,000 - $100,000</option>
                  <option value="over-100k">> $100,000</option>
                </select>
              </div>
            </div>

            {/* Additional Requirements */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {locale === 'zh' ? '其他要求' : locale === 'en' ? 'Additional Requirements' : 'Дополнительные требования'}
              </label>
              <textarea
                name="requirements"
                value={formData.requirements}
                onChange={handleInputChange}
                rows={4}
                placeholder={locale === 'zh' ? '请描述您的其他要求，如包装、认证、标签等...' :
                           locale === 'en' ? 'Please describe your other requirements such as packaging, certification, labeling, etc...' :
                           'Пожалуйста, опишите ваши другие требования, такие как упаковка, сертификация, маркировка и т.д...'}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Submit Button */}
            <div className="text-center">
              <button
                type="submit"
                disabled={isSubmitting}
                className={`px-8 py-3 rounded-lg font-semibold text-white transition-colors ${
                  isSubmitting
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                {isSubmitting
                  ? (locale === 'zh' ? '提交中...' : locale === 'en' ? 'Submitting...' : 'Отправка...')
                  : (locale === 'zh' ? '提交询价' : locale === 'en' ? 'Submit Quote' : 'Отправить запрос')
                }
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
