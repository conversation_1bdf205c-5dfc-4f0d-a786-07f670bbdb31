import { NextRequest, NextResponse } from 'next/server';
import { saveQuote, getAllQuotes } from '../../../../lib/database';
import nodemailer from 'nodemailer';

// 邮件配置
const transporter = nodemailer.createTransporter({
  host: 'smtp.gmail.com', // 您可以更换为其他邮件服务
  port: 587,
  secure: false,
  auth: {
    user: process.env.EMAIL_USER, // 需要在环境变量中设置
    pass: process.env.EMAIL_PASS  // 需要在环境变量中设置
  }
});

// POST - 提交新询价
export async function POST(request) {
  try {
    const body = await request.json();
    
    // 获取客户端信息
    const userAgent = request.headers.get('user-agent') || '';
    const forwarded = request.headers.get('x-forwarded-for');
    const ipAddress = forwarded ? forwarded.split(',')[0] : request.ip || 'unknown';
    
    // 验证必填字段
    const requiredFields = ['businessType', 'company', 'contactPerson', 'email', 'phone', 'country'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // 准备数据
    const quoteData = {
      ...body,
      userAgent,
      ipAddress,
      sourcePage: body.sourcePage || 'quote-form',
      language: body.language || 'zh'
    };

    // 保存到数据库
    const quoteId = await saveQuote(quoteData);

    // 发送邮件通知
    try {
      await sendNotificationEmail(quoteData, quoteId);
    } catch (emailError) {
      console.error('Failed to send email notification:', emailError);
      // 不因为邮件发送失败而影响询价保存
    }

    return NextResponse.json({
      success: true,
      quoteId,
      message: 'Quote submitted successfully'
    });

  } catch (error) {
    console.error('Error processing quote:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET - 获取所有询价（管理后台用）
export async function GET(request) {
  try {
    // 简单的认证检查（生产环境需要更严格的认证）
    const authHeader = request.headers.get('authorization');
    if (!authHeader || authHeader !== `Bearer ${process.env.ADMIN_TOKEN}`) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const quotes = await getAllQuotes();
    return NextResponse.json({ quotes });

  } catch (error) {
    console.error('Error fetching quotes:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 发送邮件通知
async function sendNotificationEmail(quoteData, quoteId) {
  const {
    businessType,
    company,
    contactPerson,
    email,
    phone,
    country,
    city,
    productRequirements,
    requirements,
    language
  } = quoteData;

  // 根据语言选择邮件模板
  const templates = {
    zh: {
      subject: `新询价 - ${quoteId} - ${company}`,
      html: `
        <h2>新的询价请求</h2>
        <p><strong>询价编号:</strong> ${quoteId}</p>
        <p><strong>业务类型:</strong> ${businessType}</p>
        <p><strong>公司名称:</strong> ${company}</p>
        <p><strong>联系人:</strong> ${contactPerson}</p>
        <p><strong>邮箱:</strong> ${email}</p>
        <p><strong>电话:</strong> ${phone}</p>
        <p><strong>国家:</strong> ${country}</p>
        <p><strong>城市:</strong> ${city || '未提供'}</p>
        <p><strong>产品需求:</strong> ${JSON.stringify(productRequirements, null, 2)}</p>
        <p><strong>其他要求:</strong> ${requirements || '无'}</p>
        <p><strong>提交时间:</strong> ${new Date().toLocaleString('zh-CN')}</p>
      `
    },
    en: {
      subject: `New Quote Request - ${quoteId} - ${company}`,
      html: `
        <h2>New Quote Request</h2>
        <p><strong>Quote ID:</strong> ${quoteId}</p>
        <p><strong>Business Type:</strong> ${businessType}</p>
        <p><strong>Company:</strong> ${company}</p>
        <p><strong>Contact Person:</strong> ${contactPerson}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Phone:</strong> ${phone}</p>
        <p><strong>Country:</strong> ${country}</p>
        <p><strong>City:</strong> ${city || 'Not provided'}</p>
        <p><strong>Product Requirements:</strong> ${JSON.stringify(productRequirements, null, 2)}</p>
        <p><strong>Additional Requirements:</strong> ${requirements || 'None'}</p>
        <p><strong>Submitted:</strong> ${new Date().toLocaleString('en-US')}</p>
      `
    },
    ru: {
      subject: `Новый запрос цены - ${quoteId} - ${company}`,
      html: `
        <h2>Новый запрос цены</h2>
        <p><strong>ID запроса:</strong> ${quoteId}</p>
        <p><strong>Тип бизнеса:</strong> ${businessType}</p>
        <p><strong>Компания:</strong> ${company}</p>
        <p><strong>Контактное лицо:</strong> ${contactPerson}</p>
        <p><strong>Email:</strong> ${email}</p>
        <p><strong>Телефон:</strong> ${phone}</p>
        <p><strong>Страна:</strong> ${country}</p>
        <p><strong>Город:</strong> ${city || 'Не указан'}</p>
        <p><strong>Требования к продукту:</strong> ${JSON.stringify(productRequirements, null, 2)}</p>
        <p><strong>Дополнительные требования:</strong> ${requirements || 'Нет'}</p>
        <p><strong>Отправлено:</strong> ${new Date().toLocaleString('ru-RU')}</p>
      `
    }
  };

  const template = templates[language] || templates.zh;

  const mailOptions = {
    from: process.env.EMAIL_USER,
    to: '<EMAIL>', // 接收询价的邮箱
    subject: template.subject,
    html: template.html
  };

  // 同时发送确认邮件给客户
  const customerTemplate = {
    zh: {
      subject: `询价确认 - ${quoteId}`,
      html: `
        <h2>感谢您的询价</h2>
        <p>尊敬的 ${contactPerson}，</p>
        <p>我们已收到您的询价请求，询价编号：<strong>${quoteId}</strong></p>
        <p>我们将在24小时内回复您的询价。</p>
        <p>如有紧急需求，请直接联系：</p>
        <p>聂磊 (Reggie Nie)<br>
        电话：+86 132 1615 6841<br>
        邮箱：<EMAIL></p>
        <p>杭州卡恩新型建材有限公司</p>
      `
    },
    en: {
      subject: `Quote Confirmation - ${quoteId}`,
      html: `
        <h2>Thank you for your inquiry</h2>
        <p>Dear ${contactPerson},</p>
        <p>We have received your quote request, Quote ID: <strong>${quoteId}</strong></p>
        <p>We will respond to your inquiry within 24 hours.</p>
        <p>For urgent matters, please contact directly:</p>
        <p>Reggie Nie<br>
        Phone: +86 132 1615 6841<br>
        Email: <EMAIL></p>
        <p>Hangzhou KARN New Building Materials Co., Ltd.</p>
      `
    },
    ru: {
      subject: `Подтверждение запроса - ${quoteId}`,
      html: `
        <h2>Спасибо за ваш запрос</h2>
        <p>Уважаемый ${contactPerson},</p>
        <p>Мы получили ваш запрос цены, ID запроса: <strong>${quoteId}</strong></p>
        <p>Мы ответим на ваш запрос в течение 24 часов.</p>
        <p>По срочным вопросам обращайтесь напрямую:</p>
        <p>Регги Ни<br>
        Телефон: +86 132 1615 6841<br>
        Email: <EMAIL></p>
        <p>ООО «Ханчжоу КАРН Новые Строительные Материалы»</p>
      `
    }
  };

  const customerMail = customerTemplate[language] || customerTemplate.zh;
  const customerMailOptions = {
    from: process.env.EMAIL_USER,
    to: email,
    subject: customerMail.subject,
    html: customerMail.html
  };

  // 发送两封邮件
  await Promise.all([
    transporter.sendMail(mailOptions),
    transporter.sendMail(customerMailOptions)
  ]);
}
