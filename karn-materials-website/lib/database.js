const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// 数据库文件路径
const dbPath = path.join(process.cwd(), 'data', 'quotes.db');

// 创建数据库连接
function getDatabase() {
  return new sqlite3.Database(dbPath, (err) => {
    if (err) {
      console.error('Error opening database:', err.message);
    } else {
      console.log('Connected to SQLite database');
    }
  });
}

// 初始化数据库表
function initDatabase() {
  const db = getDatabase();
  
  // 创建询价主表
  db.serialize(() => {
    db.run(`CREATE TABLE IF NOT EXISTS quotes (
      id TEXT PRIMARY KEY,
      business_type TEXT NOT NULL,
      company TEXT NOT NULL,
      contact_person TEXT NOT NULL,
      email TEXT NOT NULL,
      phone TEXT NOT NULL,
      country TEXT NOT NULL,
      city TEXT,
      product_requirements TEXT,
      delivery_terms TEXT,
      destination_port TEXT,
      delivery_time TEXT,
      payment_terms TEXT,
      requirements TEXT,
      budget_range TEXT,
      created_at TEXT NOT NULL,
      updated_at TEXT,
      status TEXT DEFAULT 'pending',
      assigned_to TEXT,
      language TEXT DEFAULT 'zh',
      source_page TEXT,
      user_agent TEXT,
      ip_address TEXT
    )`);

    // 创建产品需求详情表
    db.run(`CREATE TABLE IF NOT EXISTS quote_products (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      quote_id TEXT NOT NULL,
      product_type TEXT NOT NULL,
      product_code TEXT,
      quantity INTEGER,
      unit TEXT,
      specifications TEXT,
      FOREIGN KEY (quote_id) REFERENCES quotes(id)
    )`);

    // 创建附件表
    db.run(`CREATE TABLE IF NOT EXISTS quote_attachments (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      quote_id TEXT NOT NULL,
      filename TEXT NOT NULL,
      original_name TEXT NOT NULL,
      file_size INTEGER,
      mime_type TEXT,
      upload_time TEXT NOT NULL,
      FOREIGN KEY (quote_id) REFERENCES quotes(id)
    )`);
  });

  db.close();
}

// 保存询价信息
function saveQuote(quoteData) {
  return new Promise((resolve, reject) => {
    const db = getDatabase();
    const quoteId = generateQuoteId();
    
    const {
      businessType,
      company,
      contactPerson,
      email,
      phone,
      country,
      city,
      productRequirements,
      deliveryTerms,
      destinationPort,
      deliveryTime,
      paymentTerms,
      requirements,
      budgetRange,
      language,
      sourcePage,
      userAgent,
      ipAddress,
      products
    } = quoteData;

    db.serialize(() => {
      // 插入主记录
      db.run(`INSERT INTO quotes (
        id, business_type, company, contact_person, email, phone, country, city,
        product_requirements, delivery_terms, destination_port, delivery_time,
        payment_terms, requirements, budget_range, created_at, language,
        source_page, user_agent, ip_address
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        quoteId, businessType, company, contactPerson, email, phone, country, city,
        JSON.stringify(productRequirements), deliveryTerms, destinationPort, deliveryTime,
        paymentTerms, requirements, budgetRange, new Date().toISOString(), language,
        sourcePage, userAgent, ipAddress
      ], function(err) {
        if (err) {
          reject(err);
          return;
        }

        // 插入产品详情
        if (products && products.length > 0) {
          const stmt = db.prepare(`INSERT INTO quote_products (
            quote_id, product_type, product_code, quantity, unit, specifications
          ) VALUES (?, ?, ?, ?, ?, ?)`);

          products.forEach(product => {
            stmt.run([
              quoteId,
              product.type,
              product.code,
              product.quantity,
              product.unit,
              JSON.stringify(product.specifications || {})
            ]);
          });

          stmt.finalize();
        }

        resolve(quoteId);
      });
    });

    db.close();
  });
}

// 生成询价ID
function generateQuoteId() {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 5);
  return `Q${timestamp}${random}`.toUpperCase();
}

// 获取所有询价
function getAllQuotes() {
  return new Promise((resolve, reject) => {
    const db = getDatabase();
    
    db.all(`SELECT * FROM quotes ORDER BY created_at DESC`, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
      db.close();
    });
  });
}

// 根据ID获取询价详情
function getQuoteById(quoteId) {
  return new Promise((resolve, reject) => {
    const db = getDatabase();
    
    db.get(`SELECT * FROM quotes WHERE id = ?`, [quoteId], (err, row) => {
      if (err) {
        reject(err);
      } else if (!row) {
        resolve(null);
      } else {
        // 获取产品详情
        db.all(`SELECT * FROM quote_products WHERE quote_id = ?`, [quoteId], (err, products) => {
          if (err) {
            reject(err);
          } else {
            row.products = products;
            resolve(row);
          }
        });
      }
      db.close();
    });
  });
}

module.exports = {
  initDatabase,
  saveQuote,
  getAllQuotes,
  getQuoteById,
  generateQuoteId
};
