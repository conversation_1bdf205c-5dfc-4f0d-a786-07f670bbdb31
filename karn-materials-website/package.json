{"name": "karn-materials-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "init-db": "node scripts/init-db.js", "setup": "npm run init-db"}, "dependencies": {"@types/nodemailer": "^6.4.17", "i18next": "^25.3.2", "next": "15.4.5", "next-i18next": "^15.4.2", "nodemailer": "^7.0.5", "react": "19.1.0", "react-dom": "19.1.0", "react-i18next": "^15.6.1", "sqlite3": "^5.1.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}