# 杭州卡恩新型建材有限公司 - 外贸获客网站

## 🌟 项目概述

这是一个专为杭州卡恩新型建材有限公司设计的三语言（中文、英文、俄文）外贸获客网站。网站采用静态HTML设计，可以直接部署到Cloudflare Pages，无需复杂的后端配置。

### 核心功能
- ✅ **三语言支持** - 中文、英文、俄文实时切换
- ✅ **响应式设计** - 完美适配桌面端和移动端
- ✅ **视频展示区域** - 公司介绍、生产工艺、产品演示、客户案例四大视频模块
- ✅ **详细产品展示** - 完整的产品信息、技术参数、应用场景展示
- ✅ **产品对比功能** - 直观的产品对比表格
- ✅ **质量认证展示** - ISO 9001、CE认证等权威认证
- ✅ **客户成功案例** - 真实的合作案例和客户反馈
- ✅ **在线询价** - 智能表单收集客户需求
- ✅ **联系信息** - 多种联系方式展示
- ✅ **SEO优化** - 搜索引擎友好的结构

### 业务板块
1. **大包装原料** (20kg/袋) - 批发商和工程项目
2. **代加工服务** (OEM/ODM) - 客户品牌定制生产  
3. **小包装半成品** (200g/袋) - 零售市场

## 📁 文件结构

```
karn-website-simple/
├── index.html          # 主页
├── quote.html          # 询价页面
├── README.md           # 说明文档
└── (其他资源文件)
```

## 🚀 部署到Cloudflare Pages

### 方法一：通过Git仓库部署（推荐）

1. **创建Git仓库**
   ```bash
   cd karn-website-simple
   git init
   git add .
   git commit -m "Initial commit"
   ```

2. **推送到GitHub/GitLab**
   - 在GitHub或GitLab创建新仓库
   - 推送代码到仓库

3. **连接Cloudflare Pages**
   - 登录 [Cloudflare Dashboard](https://dash.cloudflare.com)
   - 进入 Pages 部分
   - 点击 "Create a project"
   - 选择 "Connect to Git"
   - 选择您的仓库
   - 构建设置：
     - 构建命令：留空
     - 构建输出目录：`/`
   - 点击 "Save and Deploy"

### 方法二：直接上传文件

1. **登录Cloudflare Pages**
   - 进入 [Cloudflare Pages](https://pages.cloudflare.com)
   - 点击 "Create a project"
   - 选择 "Upload assets"

2. **上传文件**
   - 将 `karn-website-simple` 文件夹中的所有文件打包成ZIP
   - 上传ZIP文件
   - 等待部署完成

## 🔧 自定义配置

### 修改联系信息
编辑 `index.html` 和 `quote.html` 中的联系信息：
- 电话：`+86 132 1615 6841`
- 邮箱：`<EMAIL>`
- 联系人：`聂磊 (Reggie Nie)`

### 添加自定义域名
1. 在Cloudflare Pages项目设置中
2. 点击 "Custom domains"
3. 添加您的域名
4. 按照提示配置DNS记录

### 修改产品信息
在 `index.html` 的JavaScript部分修改 `translations` 对象中的产品信息。

## 📧 询价表单处理

当前版本的询价表单是前端模拟提交，会显示成功消息但不会实际发送邮件。

### 升级为真实表单处理

如需真实的表单处理功能，可以：

1. **使用Cloudflare Workers**
   - 创建Worker脚本处理表单提交
   - 集成邮件发送服务（如SendGrid、Mailgun）

2. **使用第三方表单服务**
   - Formspree
   - Netlify Forms
   - Google Forms

3. **集成CRM系统**
   - HubSpot
   - Salesforce
   - 自定义CRM

## 🎨 样式自定义

网站使用Tailwind CSS框架，您可以：
- 修改颜色主题
- 调整布局样式
- 添加动画效果
- 自定义组件

## 📱 移动端优化

网站已经过移动端优化：
- 响应式导航菜单
- 触摸友好的按钮
- 优化的表单布局
- 快速加载速度

## 🌍 SEO优化建议

1. **添加更多页面**
   - 关于我们详细页
   - 产品详情页
   - 新闻/博客页面

2. **优化内容**
   - 添加更多关键词
   - 丰富产品描述
   - 添加客户案例

3. **技术优化**
   - 添加sitemap.xml
   - 优化图片压缩
   - 添加结构化数据

## 📊 数据分析

建议集成：
- Google Analytics
- Cloudflare Analytics
- 热力图工具（如Hotjar）

## 🔒 安全性

- 使用HTTPS（Cloudflare自动提供）
- 表单验证和防护
- 定期更新依赖

## 📞 技术支持

如需技术支持或功能升级，请联系开发团队。

## 📝 更新日志

### v2.0.0 (2024-07-31)
- ✅ **重大更新** - 产品展示全面升级
- ✅ **新增视频区域** - 四大视频模块展示企业实力
- ✅ **详细产品信息** - 技术参数、应用场景、使用说明
- ✅ **产品对比功能** - 直观的产品对比表格
- ✅ **质量认证展示** - 权威认证和客户案例
- ✅ **移除下载功能** - 所有信息直接在网站展示
- ✅ **优化用户体验** - 更专业的产品展示方式

### v1.0.0 (2024-07-31)
- ✅ 初始版本发布
- ✅ 三语言支持
- ✅ 响应式设计
- ✅ 基础询价表单
- ✅ 基础产品展示页面

---

**杭州卡恩新型建材有限公司**  
联系人：聂磊 (Reggie Nie)  
电话：+86 132 1615 6841  
邮箱：<EMAIL>
