export interface Quote {
  id: string
  business_type: 'bulk' | 'oem' | 'retail' | 'mixed'
  company_name: string
  contact_person: string
  email: string
  phone: string
  country: string
  city: string
  product_requirements: string
  quantity: string
  budget_range: 'under-10k' | '10k-50k' | '50k-100k' | '100k-500k' | 'over-500k'
  requirements?: string
  status: 'pending' | 'processing' | 'completed' | 'cancelled'
  priority: 'low' | 'normal' | 'high' | 'urgent'
  assigned_to?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface Product {
  id: string
  name: string
  model: string
  category: string
  description?: string
  specifications: Record<string, any>
  price_range?: string
  moq?: string
  image_url?: string
  gallery_images: string[]
  features: string[]
  applications: string[]
  is_active: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

export interface AdminUser {
  id: string
  user_id: string
  name: string
  role: 'admin' | 'manager' | 'viewer'
  permissions: Record<string, any>
  is_active: boolean
  last_login?: string
  created_at: string
}

export const BUSINESS_TYPES = {
  bulk: '大包装原料',
  oem: '代加工服务',
  retail: '小包装半成品',
  mixed: '混合业务'
} as const

export const BUDGET_RANGES = {
  'under-10k': '1万元以下',
  '10k-50k': '1-5万元',
  '50k-100k': '5-10万元',
  '100k-500k': '10-50万元',
  'over-500k': '50万元以上'
} as const

export const QUOTE_STATUS = {
  pending: '待处理',
  processing: '处理中',
  completed: '已完成',
  cancelled: '已取消'
} as const
