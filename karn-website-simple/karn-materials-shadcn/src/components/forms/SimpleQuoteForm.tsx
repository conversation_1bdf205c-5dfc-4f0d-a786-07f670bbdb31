"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
// import { supabase } from '@/lib/supabase'
// import { toast } from 'sonner'

export function SimpleQuoteForm() {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    company_name: '',
    contact_person: '',
    email: '',
    phone: '',
    requirements: ''
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // 模拟提交过程
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 显示成功消息
      alert('询价提交成功！我们将尽快联系您。\n\n注意：这是演示模式，需要配置 Supabase 后才能真正保存数据。')

      // 清空表单
      setFormData({
        company_name: '',
        contact_person: '',
        email: '',
        phone: '',
        requirements: ''
      })
    } catch (error) {
      console.error('Error:', error)
      alert('提交失败，请稍后重试。')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="max-w-md mx-auto">
      <CardHeader>
        <CardTitle>快速询价</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            placeholder="公司名称"
            value={formData.company_name}
            onChange={(e) => setFormData({...formData, company_name: e.target.value})}
            required
          />
          <Input
            placeholder="联系人"
            value={formData.contact_person}
            onChange={(e) => setFormData({...formData, contact_person: e.target.value})}
            required
          />
          <Input
            type="email"
            placeholder="邮箱"
            value={formData.email}
            onChange={(e) => setFormData({...formData, email: e.target.value})}
            required
          />
          <Input
            placeholder="电话"
            value={formData.phone}
            onChange={(e) => setFormData({...formData, phone: e.target.value})}
            required
          />
          <Textarea
            placeholder="需求描述"
            value={formData.requirements}
            onChange={(e) => setFormData({...formData, requirements: e.target.value})}
          />
          <Button type="submit" disabled={loading} className="w-full">
            {loading ? '提交中...' : '提交询价'}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
