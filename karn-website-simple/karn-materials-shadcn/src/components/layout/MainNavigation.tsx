"use client"

import { useState } from 'react'
import Link from 'next/link'
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Menu } from "lucide-react"

export function MainNavigation() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <img 
              src="/images/logos/karn-logo-main.jpg" 
              alt="KARN Materials" 
              className="w-10 h-10 rounded-lg mr-3 object-cover" 
            />
            <span className="text-xl font-bold text-green-600">KARN Materials</span>
          </Link>
          
          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            <Link href="#products" className="text-gray-700 hover:text-green-600">
              产品中心
            </Link>
            <Link href="#about" className="text-gray-700 hover:text-green-600">
              关于我们
            </Link>
            <Link href="#contact" className="text-gray-700 hover:text-green-600">
              联系我们
            </Link>
          </div>
          
          {/* Mobile Menu */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild className="lg:hidden">
              <Button variant="ghost" size="icon">
                <Menu className="h-6 w-6" />
              </Button>
            </SheetTrigger>
            <SheetContent>
              <div className="flex flex-col space-y-4 mt-8">
                <Link href="#products" onClick={() => setIsOpen(false)}>
                  产品中心
                </Link>
                <Link href="#about" onClick={() => setIsOpen(false)}>
                  关于我们
                </Link>
                <Link href="#contact" onClick={() => setIsOpen(false)}>
                  联系我们
                </Link>
              </div>
            </SheetContent>
          </Sheet>
          
          {/* CTA Button */}
          <Button className="bg-green-600 hover:bg-green-700 hidden sm:inline-flex">
            联系我们
          </Button>
        </div>
      </div>
    </nav>
  )
}
