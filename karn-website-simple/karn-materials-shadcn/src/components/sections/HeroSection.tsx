import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export function HeroSection() {
  return (
    <section 
      className="relative min-h-screen flex items-center justify-center bg-cover bg-center"
      style={{ backgroundImage: 'url(/images/hero/hero-banner.jpg)' }}
    >
      <div className="absolute inset-0 bg-black/50" />
      <div className="relative z-10 max-w-4xl mx-auto px-4 text-center text-white">
        <Badge variant="secondary" className="mb-4 bg-green-600 text-white">
          专业建材制造商
        </Badge>
        <h1 className="text-4xl md:text-6xl font-bold mb-6">
          卡恩建材
          <span className="block text-green-400">墙纸胶粉专家</span>
        </h1>
        <p className="text-xl md:text-2xl mb-8 text-gray-200">
          15年专业经验 • 50+国家出口 • 10000+吨年产能
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" className="bg-orange-500 hover:bg-orange-600">
            获取报价和价格
          </Button>
          <Button 
            size="lg" 
            variant="outline" 
            className="border-white text-white hover:bg-white hover:text-gray-900"
          >
            了解更多
          </Button>
        </div>
      </div>
    </section>
  )
}
