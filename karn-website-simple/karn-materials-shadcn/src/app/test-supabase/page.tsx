import { SimpleQuoteForm } from '@/components/forms/SimpleQuoteForm'

export default function TestSupabasePage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            🗄️ Supabase 集成测试
          </h1>
          <p className="text-lg text-gray-600">
            测试 KARN Materials 网站的 Supabase 数据库连接
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h2 className="text-xl font-semibold mb-4">测试表单</h2>
            <SimpleQuoteForm />
          </div>
          
          <div className="bg-white rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">集成状态</h2>
            <div className="space-y-3">
              <div className="flex items-center">
                <span className="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
                <span>Next.js 项目已配置</span>
              </div>
              <div className="flex items-center">
                <span className="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
                <span>Shadcn/ui 组件已安装</span>
              </div>
              <div className="flex items-center">
                <span className="w-3 h-3 bg-red-500 rounded-full mr-3"></span>
                <span>需要配置 Supabase 环境变量</span>
              </div>
              <div className="flex items-center">
                <span className="w-3 h-3 bg-red-500 rounded-full mr-3"></span>
                <span>需要创建 Supabase 数据表</span>
              </div>
              <div className="flex items-center">
                <span className="w-3 h-3 bg-blue-500 rounded-full mr-3"></span>
                <span>当前为演示模式</span>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">下一步操作：</h3>
              <ol className="text-sm text-blue-800 space-y-1">
                <li>1. 创建 Supabase 项目</li>
                <li>2. 复制 .env.local.example 为 .env.local</li>
                <li>3. 填写 Supabase 配置信息</li>
                <li>4. 在 Supabase 中创建数据表</li>
                <li>5. 测试表单提交功能</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
