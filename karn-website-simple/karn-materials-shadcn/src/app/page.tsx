import { MainNavigation } from '@/components/layout/MainNavigation'
import { HeroSection } from '@/components/sections/HeroSection'

export default function HomePage() {
  return (
    <>
      <MainNavigation />
      <HeroSection />
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            🎉 Shadcn/ui 迁移演示成功！
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            这是一个基础的演示，展示了如何使用 Shadcn/ui 组件重新构建 KARN Materials 网站。
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <h3 className="text-xl font-semibold mb-2">现代化组件</h3>
              <p className="text-gray-600">使用 Shadcn/ui 的高质量组件</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <h3 className="text-xl font-semibold mb-2">响应式设计</h3>
              <p className="text-gray-600">完美适配各种设备屏幕</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <h3 className="text-xl font-semibold mb-2">TypeScript 支持</h3>
              <p className="text-gray-600">类型安全的开发体验</p>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}
