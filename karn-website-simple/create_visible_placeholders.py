#!/usr/bin/env python3
"""
创建可见的SVG占位符图片
"""

import os

def create_svg_image(width, height, text, filename, bg_color="#228B22", text_color="#FFFFFF"):
    """创建SVG占位符图片"""
    
    # 计算字体大小
    font_size = max(24, min(width, height) // 20)
    
    # 处理多行文本
    lines = text.split('\n')
    line_height = font_size * 1.2
    total_text_height = len(lines) * line_height
    start_y = (height - total_text_height) // 2 + font_size
    
    # 创建文本元素
    text_elements = ""
    for i, line in enumerate(lines):
        y_pos = start_y + (i * line_height)
        text_elements += f'''
        <text x="{width//2}" y="{y_pos}" 
              font-family="Arial, sans-serif" 
              font-size="{font_size}" 
              font-weight="bold"
              fill="{text_color}" 
              text-anchor="middle">{line}</text>'''
    
    # 创建SVG内容
    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
    <!-- 渐变背景 -->
    <defs>
        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:{bg_color};stop-opacity:1" />
            <stop offset="100%" style="stop-color:{bg_color};stop-opacity:0.8" />
        </linearGradient>
    </defs>
    
    <!-- 背景 -->
    <rect width="{width}" height="{height}" fill="url(#grad1)"/>
    
    <!-- 装饰边框 -->
    <rect x="10" y="10" width="{width-20}" height="{height-20}" 
          fill="none" stroke="{text_color}" stroke-width="3" opacity="0.5"/>
    
    <!-- 内边框 -->
    <rect x="20" y="20" width="{width-40}" height="{height-40}" 
          fill="none" stroke="{text_color}" stroke-width="1" opacity="0.3"/>
    
    <!-- 文本 -->
    {text_elements}
    
    <!-- 装饰元素 -->
    <circle cx="{width-60}" cy="60" r="25" fill="{text_color}" opacity="0.2"/>
    <circle cx="60" cy="{height-60}" r="20" fill="{text_color}" opacity="0.2"/>
    
    <!-- 角落装饰 -->
    <polygon points="0,0 50,0 0,50" fill="{text_color}" opacity="0.1"/>
    <polygon points="{width},{height} {width-50},{height} {width},{height-50}" fill="{text_color}" opacity="0.1"/>
</svg>'''
    
    # 保存SVG文件
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(svg_content)
    
    print(f"✅ 创建SVG: {filename} ({width}x{height})")

def create_logo_svg(width, height, filename):
    """创建Logo SVG"""
    
    font_size = min(width, height) // 8
    
    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
    <!-- 渐变背景 -->
    <defs>
        <radialGradient id="logoGrad" cx="50%" cy="50%" r="50%">
            <stop offset="0%" style="stop-color:#32CD32;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#228B22;stop-opacity:1" />
        </radialGradient>
    </defs>
    
    <!-- 圆形背景 -->
    <circle cx="{width//2}" cy="{height//2}" r="{min(width, height)//2 - 20}" 
            fill="url(#logoGrad)" stroke="#FFFFFF" stroke-width="4"/>
    
    <!-- 公司名称 -->
    <text x="{width//2}" y="{height//2 - 10}" 
          font-family="Arial, sans-serif" 
          font-size="{font_size}" 
          font-weight="bold"
          fill="#FFFFFF" 
          text-anchor="middle" 
          dominant-baseline="middle">KARN</text>
    
    <!-- 副标题 -->
    <text x="{width//2}" y="{height//2 + font_size//2 + 10}" 
          font-family="Arial, sans-serif" 
          font-size="{font_size//2}" 
          fill="#FFFFFF" 
          text-anchor="middle" 
          dominant-baseline="middle">建材</text>
</svg>'''
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(svg_content)
    
    print(f"✅ 创建Logo SVG: {filename} ({width}x{height})")

def main():
    """主函数 - 生成所有可见的占位符图片"""
    
    print("🎨 开始生成可见的占位符图片...")
    
    # 确保目录存在
    base_dir = "images"
    
    # 1. 产品图片
    products_dir = os.path.join(base_dir, "products")
    create_svg_image(800, 800, "888型号\n墙纸胶粉\n产品主图", 
                    os.path.join(products_dir, "product-888-main.svg"),
                    bg_color="#4682B4")  # 钢蓝色
    
    create_svg_image(1200, 800, "产品细节展示\n高品质胶粉\n专业制造", 
                    os.path.join(products_dir, "product-888-detail-1.svg"),
                    bg_color="#5F9EA0")  # 军校蓝
    
    create_svg_image(1200, 800, "包装展示\n20kg装\n出口品质", 
                    os.path.join(products_dir, "product-packaging-showcase.svg"),
                    bg_color="#6495ED")  # 矢车菊蓝
    
    # 2. 公司介绍图片
    about_dir = os.path.join(base_dir, "about")
    create_svg_image(1920, 1080, "现代化生产工厂\n杭州卡恩新型建材\n15年专业制造", 
                    os.path.join(about_dir, "factory-overview.svg"),
                    bg_color="#228B22")  # 森林绿
    
    create_svg_image(1200, 800, "自动化生产线\n先进设备\n质量保证", 
                    os.path.join(about_dir, "production-line-1.svg"),
                    bg_color="#32CD32")  # 酸橙绿
    
    create_svg_image(1200, 800, "质量控制中心\nISO认证\n严格检测", 
                    os.path.join(about_dir, "quality-control.svg"),
                    bg_color="#3CB371")  # 中海绿
    
    # 3. Logo文件
    logos_dir = os.path.join(base_dir, "logos")
    create_logo_svg(512, 512, os.path.join(logos_dir, "karn-logo-main.svg"))
    create_logo_svg(800, 200, os.path.join(logos_dir, "karn-logo-horizontal.svg"))
    
    # 4. 证书图片
    certificates_dir = os.path.join(base_dir, "certificates")
    create_svg_image(1200, 1600, "ISO 9001:2015\n质量管理体系认证\n国际标准", 
                    os.path.join(certificates_dir, "iso-9001-certificate.svg"),
                    bg_color="#FFFFFF", text_color="#000000")
    
    create_svg_image(1200, 1600, "营业执照\n杭州卡恩新型建材\n有限公司", 
                    os.path.join(certificates_dir, "business-license.svg"),
                    bg_color="#F8F8FF", text_color="#000000")
    
    # 5. 团队照片
    team_dir = os.path.join(base_dir, "team")
    create_svg_image(1920, 1080, "专业团队\n经验丰富\n服务全球", 
                    os.path.join(team_dir, "team-photo-1.svg"),
                    bg_color="#4CAF50")
    
    create_svg_image(1200, 800, "工作现场\n专业操作\n品质保证", 
                    os.path.join(team_dir, "working-scene-1.svg"),
                    bg_color="#66BB6A")
    
    # 6. 案例展示
    gallery_dir = os.path.join(base_dir, "gallery")
    create_svg_image(1200, 800, "成功案例\n住宅项目\n优质效果", 
                    os.path.join(gallery_dir, "case-study-1.svg"),
                    bg_color="#2196F3")
    
    create_svg_image(1200, 800, "商业应用\n大型项目\n专业施工", 
                    os.path.join(gallery_dir, "application-commercial.svg"),
                    bg_color="#1976D2")
    
    # 7. 联系页面
    contact_dir = os.path.join(base_dir, "contact")
    create_svg_image(1920, 1080, "公司总部\n杭州市\n欢迎来访", 
                    os.path.join(contact_dir, "office-building.svg"),
                    bg_color="#607D8B")
    
    create_svg_image(300, 300, "微信\n二维码\n扫码联系", 
                    os.path.join(contact_dir, "wechat-qr.svg"),
                    bg_color="#4CAF50")
    
    print("\n🎉 所有可见占位符图片生成完成！")
    print("📋 生成的SVG图片列表：")
    print("   - 产品图片: 3张 (SVG)")
    print("   - 公司图片: 3张 (SVG)") 
    print("   - Logo文件: 2张 (SVG)")
    print("   - 证书图片: 2张 (SVG)")
    print("   - 团队照片: 2张 (SVG)")
    print("   - 案例展示: 2张 (SVG)")
    print("   - 联系页面: 2张 (SVG)")
    print("   总计: 16张可见的SVG占位符图片")
    print("\n💡 这些SVG图片具有正确的尺寸和可见的内容，可以在网页上正常显示。")

if __name__ == "__main__":
    main()
