#!/bin/bash

echo "🗄️ KARN Materials - Supabase 集成脚本"
echo "======================================"

# 检查是否在正确的目录
if [ ! -d "karn-materials-shadcn" ]; then
    echo "❌ 错误：请在包含 karn-materials-shadcn 文件夹的目录中运行此脚本"
    exit 1
fi

cd karn-materials-shadcn

echo "📋 当前目录: $(pwd)"

# 检查 package.json 是否存在
if [ ! -f "package.json" ]; then
    echo "❌ 错误：未找到 package.json 文件"
    exit 1
fi

echo "✅ 找到 Next.js 项目"

# 安装 Supabase 相关依赖
echo ""
echo "📦 安装 Supabase 和相关依赖..."
npm install @supabase/supabase-js react-hook-form @hookform/resolvers/zod zod lucide-react sonner date-fns recharts

# 安装 Shadcn/ui 组件
echo ""
echo "🎨 安装 Shadcn/ui 组件..."

# 检查是否已初始化 shadcn/ui
if [ ! -f "components.json" ]; then
    echo "初始化 Shadcn/ui..."
    npx shadcn-ui@latest init --defaults
fi

# 安装需要的组件
echo "安装 UI 组件..."
npx shadcn-ui@latest add button card form input select textarea navigation-menu sheet dialog badge table tabs alert-dialog dropdown-menu avatar calendar

# 创建目录结构
echo ""
echo "📁 创建项目结构..."
mkdir -p src/lib
mkdir -p src/types
mkdir -p src/components/forms
mkdir -p src/components/admin
mkdir -p src/app/admin
mkdir -p src/app/api

# 创建 Supabase 客户端配置
echo ""
echo "🔧 创建 Supabase 配置文件..."

cat > src/lib/supabase.ts << 'EOF'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// 服务端客户端 (用于管理员操作)
export const supabaseAdmin = createClient(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)
EOF

# 创建类型定义
cat > src/types/database.ts << 'EOF'
export interface Quote {
  id: string
  business_type: 'bulk' | 'oem' | 'retail' | 'mixed'
  company_name: string
  contact_person: string
  email: string
  phone: string
  country: string
  city: string
  product_requirements: string
  quantity: string
  budget_range: 'under-10k' | '10k-50k' | '50k-100k' | '100k-500k' | 'over-500k'
  requirements?: string
  status: 'pending' | 'processing' | 'completed' | 'cancelled'
  priority: 'low' | 'normal' | 'high' | 'urgent'
  assigned_to?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface Product {
  id: string
  name: string
  model: string
  category: string
  description?: string
  specifications: Record<string, any>
  price_range?: string
  moq?: string
  image_url?: string
  gallery_images: string[]
  features: string[]
  applications: string[]
  is_active: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

export interface AdminUser {
  id: string
  user_id: string
  name: string
  role: 'admin' | 'manager' | 'viewer'
  permissions: Record<string, any>
  is_active: boolean
  last_login?: string
  created_at: string
}

export const BUSINESS_TYPES = {
  bulk: '大包装原料',
  oem: '代加工服务',
  retail: '小包装半成品',
  mixed: '混合业务'
} as const

export const BUDGET_RANGES = {
  'under-10k': '1万元以下',
  '10k-50k': '1-5万元',
  '50k-100k': '5-10万元',
  '100k-500k': '10-50万元',
  'over-500k': '50万元以上'
} as const

export const QUOTE_STATUS = {
  pending: '待处理',
  processing: '处理中',
  completed: '已完成',
  cancelled: '已取消'
} as const
EOF

# 创建环境变量模板
cat > .env.local.example << 'EOF'
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# 邮件配置 (可选)
SENDGRID_API_KEY=your-sendgrid-api-key
ADMIN_EMAIL=<EMAIL>

# 网站配置
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_COMPANY_NAME=杭州卡恩新型建材有限公司
EOF

# 创建简单的询价表单组件
cat > src/components/forms/SimpleQuoteForm.tsx << 'EOF'
"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { supabase } from '@/lib/supabase'
import { toast } from 'sonner'

export function SimpleQuoteForm() {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    company_name: '',
    contact_person: '',
    email: '',
    phone: '',
    requirements: ''
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const { error } = await supabase
        .from('quotes')
        .insert([{
          business_type: 'mixed',
          company_name: formData.company_name,
          contact_person: formData.contact_person,
          email: formData.email,
          phone: formData.phone,
          country: '中国',
          city: '未填写',
          product_requirements: '咨询',
          quantity: '待确认',
          budget_range: 'under-10k',
          requirements: formData.requirements
        }])

      if (error) throw error

      toast.success('询价提交成功！我们将尽快联系您。')
      setFormData({
        company_name: '',
        contact_person: '',
        email: '',
        phone: '',
        requirements: ''
      })
    } catch (error) {
      console.error('Error:', error)
      toast.error('提交失败，请稍后重试。')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="max-w-md mx-auto">
      <CardHeader>
        <CardTitle>快速询价</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            placeholder="公司名称"
            value={formData.company_name}
            onChange={(e) => setFormData({...formData, company_name: e.target.value})}
            required
          />
          <Input
            placeholder="联系人"
            value={formData.contact_person}
            onChange={(e) => setFormData({...formData, contact_person: e.target.value})}
            required
          />
          <Input
            type="email"
            placeholder="邮箱"
            value={formData.email}
            onChange={(e) => setFormData({...formData, email: e.target.value})}
            required
          />
          <Input
            placeholder="电话"
            value={formData.phone}
            onChange={(e) => setFormData({...formData, phone: e.target.value})}
            required
          />
          <Textarea
            placeholder="需求描述"
            value={formData.requirements}
            onChange={(e) => setFormData({...formData, requirements: e.target.value})}
          />
          <Button type="submit" disabled={loading} className="w-full">
            {loading ? '提交中...' : '提交询价'}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
EOF

# 创建测试页面
cat > src/app/test-supabase/page.tsx << 'EOF'
import { SimpleQuoteForm } from '@/components/forms/SimpleQuoteForm'
import { Toaster } from 'sonner'

export default function TestSupabasePage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            🗄️ Supabase 集成测试
          </h1>
          <p className="text-lg text-gray-600">
            测试 KARN Materials 网站的 Supabase 数据库连接
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h2 className="text-xl font-semibold mb-4">测试表单</h2>
            <SimpleQuoteForm />
          </div>
          
          <div className="bg-white rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">集成状态</h2>
            <div className="space-y-3">
              <div className="flex items-center">
                <span className="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
                <span>Next.js 项目已配置</span>
              </div>
              <div className="flex items-center">
                <span className="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
                <span>Shadcn/ui 组件已安装</span>
              </div>
              <div className="flex items-center">
                <span className="w-3 h-3 bg-yellow-500 rounded-full mr-3"></span>
                <span>需要配置 Supabase 环境变量</span>
              </div>
              <div className="flex items-center">
                <span className="w-3 h-3 bg-yellow-500 rounded-full mr-3"></span>
                <span>需要创建 Supabase 数据表</span>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">下一步操作：</h3>
              <ol className="text-sm text-blue-800 space-y-1">
                <li>1. 创建 Supabase 项目</li>
                <li>2. 复制 .env.local.example 为 .env.local</li>
                <li>3. 填写 Supabase 配置信息</li>
                <li>4. 在 Supabase 中创建数据表</li>
                <li>5. 测试表单提交功能</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
      <Toaster />
    </div>
  )
}
EOF

# 更新 .gitignore
if [ -f ".gitignore" ]; then
    echo "" >> .gitignore
    echo "# Environment variables" >> .gitignore
    echo ".env.local" >> .gitignore
    echo ".env" >> .gitignore
else
    cat > .gitignore << 'EOF'
# Dependencies
node_modules/

# Next.js
.next/
out/

# Environment variables
.env.local
.env

# Logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db
EOF
fi

echo ""
echo "✅ Supabase 集成基础配置完成！"
echo ""
echo "🎯 下一步操作："
echo "1. 创建 Supabase 项目: https://supabase.com"
echo "2. 复制环境变量: cp .env.local.example .env.local"
echo "3. 填写 Supabase 配置信息到 .env.local"
echo "4. 在 Supabase 中执行 SQL 创建数据表"
echo "5. 启动开发服务器: npm run dev"
echo "6. 访问测试页面: http://localhost:3000/test-supabase"
echo ""
echo "📚 查看完整集成指南: ../SUPABASE_INTEGRATION_GUIDE.md"
