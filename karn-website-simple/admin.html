<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - 杭州卡恩新型建材有限公司</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // 简单的认证和数据管理
        let isAuthenticated = false;
        let quotes = [];

        // 模拟的询价数据
        const sampleQuotes = [
            {
                id: 'Q1722408000ABC',
                businessType: 'bulk',
                company: 'ABC Trading Co.',
                contactPerson: '<PERSON>',
                email: '<EMAIL>',
                phone: '******-0123',
                country: 'USA',
                city: 'New York',
                quantity: '50 tons/month',
                budgetRange: '50k-100k',
                requirements: 'Need bulk materials for construction projects',
                createdAt: '2024-07-31 10:30:00',
                status: 'pending'
            },
            {
                id: 'Q1722408100DEF',
                businessType: 'oem',
                company: 'DEF Materials Ltd.',
                contactPerson: '<PERSON>',
                email: '<EMAIL>',
                phone: '+*************',
                country: 'Spain',
                city: 'Madrid',
                quantity: '10 tons/month',
                budgetRange: '10k-50k',
                requirements: 'OEM service for private label products',
                createdAt: '2024-07-31 14:15:00',
                status: 'processing'
            },
            {
                id: 'Q1722408200GHI',
                businessType: 'retail',
                company: 'GHI Retail Chain',
                contactPerson: 'Alexei Petrov',
                email: '<EMAIL>',
                phone: '******-1234567',
                country: 'Russia',
                city: 'Moscow',
                quantity: '1000 packages/month',
                budgetRange: 'under-10k',
                requirements: 'Small packaging for retail stores',
                createdAt: '2024-07-31 16:45:00',
                status: 'completed'
            }
        ];

        function login() {
            const password = document.getElementById('password').value;
            if (password === 'karn2024') {
                isAuthenticated = true;
                quotes = [...sampleQuotes];
                showDashboard();
            } else {
                alert('密码错误');
            }
        }

        function logout() {
            isAuthenticated = false;
            showLogin();
        }

        function showLogin() {
            document.getElementById('login-section').classList.remove('hidden');
            document.getElementById('dashboard-section').classList.add('hidden');
        }

        function showDashboard() {
            document.getElementById('login-section').classList.add('hidden');
            document.getElementById('dashboard-section').classList.remove('hidden');
            renderQuotes();
        }

        function getBusinessTypeLabel(type) {
            const labels = {
                bulk: '大包装原料',
                oem: '代加工服务',
                retail: '小包装半成品',
                mixed: '混合业务'
            };
            return labels[type] || type;
        }

        function getStatusLabel(status) {
            const labels = {
                pending: '待处理',
                processing: '处理中',
                completed: '已完成',
                cancelled: '已取消'
            };
            return labels[status] || status;
        }

        function getStatusColor(status) {
            const colors = {
                pending: 'bg-yellow-100 text-yellow-800',
                processing: 'bg-blue-100 text-blue-800',
                completed: 'bg-green-100 text-green-800',
                cancelled: 'bg-red-100 text-red-800'
            };
            return colors[status] || 'bg-gray-100 text-gray-800';
        }

        function renderQuotes() {
            const tbody = document.getElementById('quotes-tbody');
            tbody.innerHTML = '';

            quotes.forEach(quote => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">${quote.id}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${quote.company}</div>
                        <div class="text-sm text-gray-500">${quote.contactPerson}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                            ${getBusinessTypeLabel(quote.businessType)}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${quote.email}</div>
                        <div class="text-sm text-gray-500">${quote.phone}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${quote.country}</div>
                        <div class="text-sm text-gray-500">${quote.city || ''}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(quote.status)}">
                            ${getStatusLabel(quote.status)}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${quote.createdAt}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="viewQuote('${quote.id}')" class="text-blue-600 hover:text-blue-900">查看</button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // 更新统计
            document.getElementById('total-quotes').textContent = quotes.length;
            document.getElementById('pending-quotes').textContent = quotes.filter(q => q.status === 'pending').length;
            document.getElementById('processing-quotes').textContent = quotes.filter(q => q.status === 'processing').length;
            document.getElementById('completed-quotes').textContent = quotes.filter(q => q.status === 'completed').length;
        }

        function viewQuote(quoteId) {
            const quote = quotes.find(q => q.id === quoteId);
            if (quote) {
                const modal = document.getElementById('quote-modal');
                document.getElementById('modal-quote-id').textContent = quote.id;
                document.getElementById('modal-company').textContent = quote.company;
                document.getElementById('modal-contact').textContent = quote.contactPerson;
                document.getElementById('modal-email').textContent = quote.email;
                document.getElementById('modal-phone').textContent = quote.phone;
                document.getElementById('modal-location').textContent = `${quote.country}${quote.city ? ', ' + quote.city : ''}`;
                document.getElementById('modal-business-type').textContent = getBusinessTypeLabel(quote.businessType);
                document.getElementById('modal-quantity').textContent = quote.quantity || '未提供';
                document.getElementById('modal-budget').textContent = quote.budgetRange || '未提供';
                document.getElementById('modal-requirements').textContent = quote.requirements || '无';
                document.getElementById('modal-status').textContent = getStatusLabel(quote.status);
                document.getElementById('modal-created').textContent = quote.createdAt;
                
                modal.classList.remove('hidden');
            }
        }

        function closeModal() {
            document.getElementById('quote-modal').classList.add('hidden');
        }

        // 页面加载时检查认证状态
        document.addEventListener('DOMContentLoaded', function() {
            showLogin();
        });
    </script>
</head>
<body class="bg-gray-50">
    <!-- 登录界面 -->
    <div id="login-section" class="min-h-screen flex items-center justify-center">
        <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
            <div class="text-center mb-6">
                <h1 class="text-2xl font-bold text-gray-900">管理后台登录</h1>
                <p class="text-gray-600 mt-2">杭州卡恩新型建材有限公司</p>
            </div>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">管理员密码</label>
                    <input 
                        type="password" 
                        id="password" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="请输入管理员密码"
                        onkeypress="if(event.key==='Enter') login()"
                    >
                </div>
                
                <button 
                    onclick="login()" 
                    class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                >
                    登录
                </button>
            </div>
            
            <div class="mt-6 text-center">
                <a href="index.html" class="text-blue-600 hover:text-blue-800 text-sm">返回首页</a>
            </div>
            
            <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                <p class="text-xs text-yellow-800">
                    <strong>演示密码:</strong> karn2024<br>
                    <strong>注意:</strong> 这是演示版本，数据为模拟数据
                </p>
            </div>
        </div>
    </div>

    <!-- 管理后台界面 -->
    <div id="dashboard-section" class="hidden min-h-screen">
        <!-- 顶部导航 -->
        <nav class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <h1 class="text-xl font-bold text-gray-900">询价管理后台</h1>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600">管理员</span>
                        <button onclick="logout()" class="text-red-600 hover:text-red-800 text-sm">退出登录</button>
                        <a href="index.html" class="text-blue-600 hover:text-blue-800 text-sm">返回首页</a>
                    </div>
                </div>
            </div>
        </nav>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- 统计卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="text-2xl font-bold text-blue-600" id="total-quotes">0</div>
                    <div class="text-sm text-gray-600">总询价数</div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="text-2xl font-bold text-yellow-600" id="pending-quotes">0</div>
                    <div class="text-sm text-gray-600">待处理</div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="text-2xl font-bold text-blue-600" id="processing-quotes">0</div>
                    <div class="text-sm text-gray-600">处理中</div>
                </div>
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="text-2xl font-bold text-green-600" id="completed-quotes">0</div>
                    <div class="text-sm text-gray-600">已完成</div>
                </div>
            </div>

            <!-- 询价列表 -->
            <div class="bg-white shadow-lg rounded-lg overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">询价列表</h2>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">询价编号</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">公司信息</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">业务类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">地区</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提交时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody id="quotes-tbody" class="bg-white divide-y divide-gray-200">
                            <!-- 动态生成的询价记录 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    <!-- 询价详情模态框 -->
    <div id="quote-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">询价详情</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                    <span class="text-2xl">&times;</span>
                </button>
            </div>

            <div class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">询价编号</label>
                        <p id="modal-quote-id" class="text-sm text-gray-900 font-mono"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">状态</label>
                        <p id="modal-status" class="text-sm text-gray-900"></p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">公司名称</label>
                        <p id="modal-company" class="text-sm text-gray-900"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">联系人</label>
                        <p id="modal-contact" class="text-sm text-gray-900"></p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">邮箱</label>
                        <p id="modal-email" class="text-sm text-gray-900"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">电话</label>
                        <p id="modal-phone" class="text-sm text-gray-900"></p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">地区</label>
                        <p id="modal-location" class="text-sm text-gray-900"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">业务类型</label>
                        <p id="modal-business-type" class="text-sm text-gray-900"></p>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">预计数量</label>
                        <p id="modal-quantity" class="text-sm text-gray-900"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">预算范围</label>
                        <p id="modal-budget" class="text-sm text-gray-900"></p>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">其他要求</label>
                    <p id="modal-requirements" class="text-sm text-gray-900 bg-gray-50 p-3 rounded-md"></p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">提交时间</label>
                    <p id="modal-created" class="text-sm text-gray-900"></p>
                </div>
            </div>

            <div class="flex justify-end mt-6 space-x-3">
                <button onclick="closeModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                    关闭
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    回复询价
                </button>
            </div>
        </div>
    </div>
</body>
</html>
    </div>
