<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线询价 - 杭州卡恩新型建材有限公司</title>
    <meta name="description" content="在线提交询价需求，我们将在24小时内回复您的询价">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // 简单的多语言支持
        const translations = {
            zh: {
                title: "在线询价",
                subtitle: "请填写以下信息，我们将在24小时内回复您的询价",
                form: {
                    businessType: "业务类型",
                    company: "公司名称",
                    contactPerson: "联系人",
                    email: "邮箱",
                    phone: "电话",
                    country: "国家",
                    city: "城市",
                    productRequirements: "产品需求",
                    quantity: "预计数量",
                    budgetRange: "预算范围",
                    requirements: "其他要求",
                    submit: "提交询价",
                    submitting: "提交中..."
                },
                businessTypes: {
                    bulk: "大包装原料",
                    oem: "代加工服务", 
                    retail: "小包装半成品",
                    mixed: "混合业务"
                },
                placeholders: {
                    selectBusinessType: "请选择业务类型",
                    selectBudgetRange: "请选择预算范围",
                    quantityExample: "例如：10吨/月",
                    requirementsExample: "请描述您的其他要求，如包装、认证、标签等..."
                }
            },
            en: {
                title: "Request Quote",
                subtitle: "Please fill out the information below, we will respond to your inquiry within 24 hours",
                form: {
                    businessType: "Business Type",
                    company: "Company Name",
                    contactPerson: "Contact Person",
                    email: "Email",
                    phone: "Phone",
                    country: "Country",
                    city: "City",
                    productRequirements: "Product Requirements",
                    quantity: "Estimated Quantity",
                    budgetRange: "Budget Range",
                    requirements: "Additional Requirements",
                    submit: "Submit Quote",
                    submitting: "Submitting..."
                },
                businessTypes: {
                    bulk: "Bulk Raw Materials",
                    oem: "OEM Services",
                    retail: "Retail Packages",
                    mixed: "Mixed Business"
                },
                placeholders: {
                    selectBusinessType: "Select business type",
                    selectBudgetRange: "Select budget range",
                    quantityExample: "e.g., 10 tons/month",
                    requirementsExample: "Please describe your other requirements such as packaging, certification, labeling, etc..."
                }
            },
            ru: {
                title: "Запрос цены",
                subtitle: "Пожалуйста, заполните информацию ниже, мы ответим на ваш запрос в течение 24 часов",
                form: {
                    businessType: "Тип бизнеса",
                    company: "Название компании",
                    contactPerson: "Контактное лицо",
                    email: "Email",
                    phone: "Телефон",
                    country: "Страна",
                    city: "Город",
                    productRequirements: "Требования к продукту",
                    quantity: "Ожидаемое количество",
                    budgetRange: "Бюджетный диапазон",
                    requirements: "Дополнительные требования",
                    submit: "Отправить запрос",
                    submitting: "Отправка..."
                },
                businessTypes: {
                    bulk: "Оптовые материалы",
                    oem: "OEM услуги",
                    retail: "Розничные упаковки",
                    mixed: "Смешанный бизнес"
                },
                placeholders: {
                    selectBusinessType: "Выберите тип бизнеса",
                    selectBudgetRange: "Выберите бюджетный диапазон",
                    quantityExample: "например: 10 тонн/месяц",
                    requirementsExample: "Пожалуйста, опишите ваши другие требования, такие как упаковка, сертификация, маркировка и т.д..."
                }
            }
        };

        let currentLang = 'zh';

        function changeLanguage(lang) {
            currentLang = lang;
            updateContent();
            localStorage.setItem('language', lang);
            
            // 更新语言按钮状态
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.classList.remove('bg-blue-600', 'text-white');
                btn.classList.add('bg-gray-100', 'text-gray-700');
            });
            document.querySelector(`[data-lang="${lang}"]`).classList.remove('bg-gray-100', 'text-gray-700');
            document.querySelector(`[data-lang="${lang}"]`).classList.add('bg-blue-600', 'text-white');
        }

        function updateContent() {
            const t = translations[currentLang];
            
            // 更新标题
            document.getElementById('page-title').textContent = t.title;
            document.getElementById('page-subtitle').textContent = t.subtitle;
            
            // 更新表单标签
            document.getElementById('label-businessType').textContent = t.form.businessType + ' *';
            document.getElementById('label-company').textContent = t.form.company + ' *';
            document.getElementById('label-contactPerson').textContent = t.form.contactPerson + ' *';
            document.getElementById('label-email').textContent = t.form.email + ' *';
            document.getElementById('label-phone').textContent = t.form.phone + ' *';
            document.getElementById('label-country').textContent = t.form.country + ' *';
            document.getElementById('label-city').textContent = t.form.city;
            document.getElementById('label-productRequirements').textContent = t.form.productRequirements;
            document.getElementById('label-quantity').textContent = t.form.quantity;
            document.getElementById('label-budgetRange').textContent = t.form.budgetRange;
            document.getElementById('label-requirements').textContent = t.form.requirements;
            
            // 更新选项
            document.getElementById('option-select-business').textContent = t.placeholders.selectBusinessType;
            document.getElementById('option-bulk').textContent = t.businessTypes.bulk;
            document.getElementById('option-oem').textContent = t.businessTypes.oem;
            document.getElementById('option-retail').textContent = t.businessTypes.retail;
            document.getElementById('option-mixed').textContent = t.businessTypes.mixed;
            
            document.getElementById('option-select-budget').textContent = t.placeholders.selectBudgetRange;
            
            // 更新占位符
            document.getElementById('input-quantity').placeholder = t.placeholders.quantityExample;
            document.getElementById('textarea-requirements').placeholder = t.placeholders.requirementsExample;
            
            // 更新按钮
            document.getElementById('submit-btn').textContent = t.form.submit;
        }

        // 表单提交处理
        function handleSubmit(event) {
            event.preventDefault();
            
            const submitBtn = document.getElementById('submit-btn');
            const originalText = submitBtn.textContent;
            const t = translations[currentLang];
            
            submitBtn.textContent = t.form.submitting;
            submitBtn.disabled = true;
            
            // 收集表单数据
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());
            
            // 模拟提交过程
            setTimeout(() => {
                // 显示成功消息
                const successMessage = document.getElementById('success-message');
                const quoteId = 'Q' + Date.now().toString(36).toUpperCase();
                
                if (currentLang === 'zh') {
                    successMessage.innerHTML = `<strong>询价提交成功！</strong><br>询价编号：${quoteId}<br>我们将在24小时内回复您。`;
                } else if (currentLang === 'en') {
                    successMessage.innerHTML = `<strong>Quote submitted successfully!</strong><br>Quote ID: ${quoteId}<br>We will respond within 24 hours.`;
                } else {
                    successMessage.innerHTML = `<strong>Запрос успешно отправлен!</strong><br>ID запроса: ${quoteId}<br>Мы ответим в течение 24 часов.`;
                }
                
                successMessage.classList.remove('hidden');
                
                // 重置表单
                event.target.reset();
                
                // 恢复按钮
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
                
                // 滚动到成功消息
                successMessage.scrollIntoView({ behavior: 'smooth' });
                
            }, 2000);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            const savedLang = localStorage.getItem('language') || 'zh';
            changeLanguage(savedLang);
        });
    </script>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="index.html" class="text-xl font-bold text-blue-600">KARN Materials</a>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- 语言切换 -->
                    <div class="flex gap-2">
                        <button onclick="changeLanguage('zh')" data-lang="zh" class="lang-btn px-3 py-1 rounded-md text-sm font-medium bg-blue-600 text-white">🇨🇳 中文</button>
                        <button onclick="changeLanguage('en')" data-lang="en" class="lang-btn px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700">🇺🇸 English</button>
                        <button onclick="changeLanguage('ru')" data-lang="ru" class="lang-btn px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700">🇷🇺 Русский</button>
                    </div>
                    
                    <a href="index.html" class="text-gray-600 hover:text-blue-600">返回首页</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <div class="text-center mb-8">
                <h1 id="page-title" class="text-3xl font-bold text-gray-900 mb-4">在线询价</h1>
                <p id="page-subtitle" class="text-gray-600">请填写以下信息，我们将在24小时内回复您的询价</p>
            </div>

            <!-- 成功消息 -->
            <div id="success-message" class="hidden mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-md"></div>

            <form onsubmit="handleSubmit(event)" class="space-y-6">
                <!-- 业务类型 -->
                <div>
                    <label id="label-businessType" class="block text-sm font-medium text-gray-700 mb-2">业务类型 *</label>
                    <select name="businessType" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option id="option-select-business" value="">请选择业务类型</option>
                        <option id="option-bulk" value="bulk">大包装原料</option>
                        <option id="option-oem" value="oem">代加工服务</option>
                        <option id="option-retail" value="retail">小包装半成品</option>
                        <option id="option-mixed" value="mixed">混合业务</option>
                    </select>
                </div>

                <!-- 公司信息 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label id="label-company" class="block text-sm font-medium text-gray-700 mb-2">公司名称 *</label>
                        <input type="text" name="company" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label id="label-contactPerson" class="block text-sm font-medium text-gray-700 mb-2">联系人 *</label>
                        <input type="text" name="contactPerson" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label id="label-email" class="block text-sm font-medium text-gray-700 mb-2">邮箱 *</label>
                        <input type="email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label id="label-phone" class="block text-sm font-medium text-gray-700 mb-2">电话 *</label>
                        <input type="tel" name="phone" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label id="label-country" class="block text-sm font-medium text-gray-700 mb-2">国家 *</label>
                        <input type="text" name="country" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label id="label-city" class="block text-sm font-medium text-gray-700 mb-2">城市</label>
                        <input type="text" name="city" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <!-- 产品需求 -->
                <div>
                    <label id="label-productRequirements" class="block text-sm font-medium text-gray-700 mb-4">产品需求</label>
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" name="productRequirements[]" value="bulk" class="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="text-sm text-gray-700">大包装原料</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="productRequirements[]" value="oem" class="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="text-sm text-gray-700">代加工服务</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="productRequirements[]" value="retail" class="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <span class="text-sm text-gray-700">小包装半成品</span>
                        </label>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label id="label-quantity" class="block text-sm font-medium text-gray-700 mb-2">预计数量</label>
                        <input id="input-quantity" type="text" name="quantity" placeholder="例如：10吨/月" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label id="label-budgetRange" class="block text-sm font-medium text-gray-700 mb-2">预算范围</label>
                        <select name="budgetRange" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option id="option-select-budget" value="">请选择预算范围</option>
                            <option value="under-10k">< $10,000</option>
                            <option value="10k-50k">$10,000 - $50,000</option>
                            <option value="50k-100k">$50,000 - $100,000</option>
                            <option value="over-100k">> $100,000</option>
                        </select>
                    </div>
                </div>

                <!-- 其他要求 -->
                <div>
                    <label id="label-requirements" class="block text-sm font-medium text-gray-700 mb-2">其他要求</label>
                    <textarea id="textarea-requirements" name="requirements" rows="4" placeholder="请描述您的其他要求，如包装、认证、标签等..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>

                <!-- 提交按钮 -->
                <div class="text-center">
                    <button id="submit-btn" type="submit" class="px-8 py-3 rounded-lg font-semibold text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                        提交询价
                    </button>
                </div>
            </form>
        </div>

        <!-- 联系信息 -->
        <div class="mt-12 bg-white rounded-lg shadow-lg p-8">
            <div class="text-center mb-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">或直接联系我们</h2>
                <p class="text-gray-600">我们期待与您的合作</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div>
                    <div class="text-blue-600 text-2xl mb-2">📞</div>
                    <div class="font-semibold text-gray-900">电话</div>
                    <a href="tel:+8613216156841" class="text-blue-600 hover:underline">+86 132 1615 6841</a>
                </div>
                <div>
                    <div class="text-blue-600 text-2xl mb-2">✉️</div>
                    <div class="font-semibold text-gray-900">邮箱</div>
                    <a href="mailto:<EMAIL>" class="text-blue-600 hover:underline"><EMAIL></a>
                </div>
                <div>
                    <div class="text-blue-600 text-2xl mb-2">👤</div>
                    <div class="font-semibold text-gray-900">联系人</div>
                    <div class="text-gray-600">聂磊 (Reggie Nie)</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-gray-900 text-white py-12 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h3 class="text-xl font-bold mb-4">杭州卡恩新型建材有限公司</h3>
                <p class="text-gray-400 mb-4">用品质说话</p>
                <div class="text-sm text-gray-400">
                    © 2024 杭州卡恩新型建材有限公司. 保留所有权利.
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
