// src/app/admin/quotes/page.tsx
"use client"

import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import { Quote, BUSINESS_TYPES, BUDGET_RANGES, QUOTE_STATUS } from '@/types/database'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { Eye, Edit, Trash2, Search, Filter, Download, Bell, Users, TrendingUp, Clock } from 'lucide-react'
import { toast } from 'sonner'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'

export default function QuotesAdminPage() {
  const [quotes, setQuotes] = useState<Quote[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedQuote, setSelectedQuote] = useState<Quote | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    processing: 0,
    completed: 0
  })

  useEffect(() => {
    fetchQuotes()
    
    // 实时订阅新询价
    const subscription = supabase
      .channel('quotes_admin')
      .on('postgres_changes', 
        { event: 'INSERT', schema: 'public', table: 'quotes' },
        (payload) => {
          const newQuote = payload.new as Quote
          setQuotes(prev => [newQuote, ...prev])
          updateStats(prev => ({
            ...prev,
            total: prev.total + 1,
            pending: prev.pending + 1
          }))
          toast.success(`收到新询价：${newQuote.company_name}`)
        }
      )
      .on('postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'quotes' },
        (payload) => {
          const updatedQuote = payload.new as Quote
          setQuotes(prev => 
            prev.map(quote => 
              quote.id === updatedQuote.id ? updatedQuote : quote
            )
          )
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  async function fetchQuotes() {
    try {
      const { data, error } = await supabase
        .from('quotes')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      
      setQuotes(data || [])
      calculateStats(data || [])
    } catch (error) {
      console.error('Error fetching quotes:', error)
      toast.error('获取询价数据失败')
    } finally {
      setLoading(false)
    }
  }

  function calculateStats(quotesData: Quote[]) {
    const stats = quotesData.reduce((acc, quote) => {
      acc.total++
      acc[quote.status as keyof typeof acc]++
      return acc
    }, { total: 0, pending: 0, processing: 0, completed: 0 })
    
    setStats(stats)
  }

  async function updateQuoteStatus(id: string, status: Quote['status']) {
    try {
      const { error } = await supabase
        .from('quotes')
        .update({ status, updated_at: new Date().toISOString() })
        .eq('id', id)

      if (error) throw error
      
      toast.success('状态更新成功')
    } catch (error) {
      console.error('Error updating quote:', error)
      toast.error('状态更新失败')
    }
  }

  async function deleteQuote(id: string) {
    try {
      const { error } = await supabase
        .from('quotes')
        .delete()
        .eq('id', id)

      if (error) throw error
      
      setQuotes(prev => prev.filter(quote => quote.id !== id))
      toast.success('询价已删除')
    } catch (error) {
      console.error('Error deleting quote:', error)
      toast.error('删除失败')
    }
  }

  const filteredQuotes = quotes.filter(quote => {
    const matchesSearch = 
      quote.company_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      quote.contact_person.toLowerCase().includes(searchTerm.toLowerCase()) ||
      quote.email.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || quote.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const getStatusBadgeVariant = (status: Quote['status']) => {
    switch (status) {
      case 'pending': return 'destructive'
      case 'processing': return 'default'
      case 'completed': return 'secondary'
      case 'cancelled': return 'outline'
      default: return 'default'
    }
  }

  const getPriorityColor = (priority: Quote['priority']) => {
    switch (priority) {
      case 'urgent': return 'text-red-600'
      case 'high': return 'text-orange-600'
      case 'normal': return 'text-blue-600'
      case 'low': return 'text-gray-600'
      default: return 'text-gray-600'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载询价数据中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">询价管理</h1>
              <p className="text-gray-600">管理和跟踪客户询价</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                导出数据
              </Button>
              <Button variant="outline" size="sm">
                <Bell className="h-4 w-4 mr-2" />
                通知设置
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">总询价数</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">待处理</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.pending}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">处理中</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.processing}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="h-8 w-8 bg-green-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">✓</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">已完成</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.completed}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 搜索和筛选 */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="搜索公司名称、联系人或邮箱..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有状态</SelectItem>
                    <SelectItem value="pending">待处理</SelectItem>
                    <SelectItem value="processing">处理中</SelectItem>
                    <SelectItem value="completed">已完成</SelectItem>
                    <SelectItem value="cancelled">已取消</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 询价列表 */}
        <Card>
          <CardHeader>
            <CardTitle>询价列表 ({filteredQuotes.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>公司信息</TableHead>
                    <TableHead>联系方式</TableHead>
                    <TableHead>业务类型</TableHead>
                    <TableHead>产品需求</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>提交时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredQuotes.map((quote) => (
                    <TableRow key={quote.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{quote.company_name}</div>
                          <div className="text-sm text-gray-500">{quote.contact_person}</div>
                          <div className="text-xs text-gray-400">{quote.country} • {quote.city}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{quote.email}</div>
                          <div className="text-gray-500">{quote.phone}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {BUSINESS_TYPES[quote.business_type]}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{quote.product_requirements}</div>
                          <div className="text-gray-500">数量: {quote.quantity}</div>
                          <div className="text-gray-500">预算: {BUDGET_RANGES[quote.budget_range]}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Select
                          value={quote.status}
                          onValueChange={(value) => updateQuoteStatus(quote.id, value as Quote['status'])}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="pending">待处理</SelectItem>
                            <SelectItem value="processing">处理中</SelectItem>
                            <SelectItem value="completed">已完成</SelectItem>
                            <SelectItem value="cancelled">已取消</SelectItem>
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {format(new Date(quote.created_at), 'yyyy-MM-dd HH:mm', { locale: zhCN })}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button variant="ghost" size="sm" onClick={() => setSelectedQuote(quote)}>
                                <Eye className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl">
                              <DialogHeader>
                                <DialogTitle>询价详情</DialogTitle>
                              </DialogHeader>
                              {selectedQuote && (
                                <div className="space-y-4">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <label className="text-sm font-medium">公司名称</label>
                                      <p className="text-sm text-gray-600">{selectedQuote.company_name}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">联系人</label>
                                      <p className="text-sm text-gray-600">{selectedQuote.contact_person}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">邮箱</label>
                                      <p className="text-sm text-gray-600">{selectedQuote.email}</p>
                                    </div>
                                    <div>
                                      <label className="text-sm font-medium">电话</label>
                                      <p className="text-sm text-gray-600">{selectedQuote.phone}</p>
                                    </div>
                                  </div>
                                  {selectedQuote.requirements && (
                                    <div>
                                      <label className="text-sm font-medium">其他要求</label>
                                      <p className="text-sm text-gray-600 mt-1">{selectedQuote.requirements}</p>
                                    </div>
                                  )}
                                </div>
                              )}
                            </DialogContent>
                          </Dialog>
                          
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <Trash2 className="h-4 w-4 text-red-600" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>确认删除</AlertDialogTitle>
                                <AlertDialogDescription>
                                  确定要删除来自 {quote.company_name} 的询价吗？此操作无法撤销。
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>取消</AlertDialogCancel>
                                <AlertDialogAction onClick={() => deleteQuote(quote.id)}>
                                  删除
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            
            {filteredQuotes.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500">暂无询价数据</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
