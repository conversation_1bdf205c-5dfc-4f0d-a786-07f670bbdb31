# 🗄️ Supabase 集成指南 - KARN Materials 网站
=====================================================

## 📋 **当前状态分析**

### **❌ 当前网站后端状态：**
- **没有使用Supabase** - 纯静态HTML网站
- **没有后端数据库** - 表单提交无数据存储
- **没有用户认证** - 管理后台无身份验证
- **没有实时功能** - 无法实时接收询价通知

### **✅ 已有基础：**
- Next.js 项目框架 (`karn-materials-shadcn`)
- Tailwind CSS 样式系统
- 基础的表单结构

## 🎯 **集成目标**

将网站升级为具有以下功能的现代化应用：
- ✅ **询价数据存储** - 所有询价信息保存到数据库
- ✅ **管理后台** - 管理员可查看和处理询价
- ✅ **实时通知** - 新询价实时推送给管理员
- ✅ **用户认证** - 安全的管理员登录系统
- ✅ **数据分析** - 询价统计和趋势分析

## 🔧 **第一步：Supabase 项目设置**

### **1.1 创建 Supabase 项目**
```bash
# 访问 https://supabase.com
# 1. 注册/登录账号
# 2. 点击 "New Project"
# 3. 选择组织
# 4. 项目名称: karn-materials
# 5. 数据库密码: 设置强密码
# 6. 区域: 选择 Asia Pacific (Singapore) - 距离中国最近
# 7. 点击 "Create new project"
```

### **1.2 获取项目配置**
```bash
# 项目创建完成后，在 Settings > API 中获取：
# - Project URL: https://your-project.supabase.co
# - anon public key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
# - service_role key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 🗄️ **第二步：数据库设计**

### **2.1 创建数据表**
在 Supabase Dashboard > SQL Editor 中执行以下SQL：

```sql
-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 询价表
CREATE TABLE quotes (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  business_type TEXT NOT NULL CHECK (business_type IN ('bulk', 'oem', 'retail', 'mixed')),
  company_name TEXT NOT NULL,
  contact_person TEXT NOT NULL,
  email TEXT NOT NULL,
  phone TEXT NOT NULL,
  country TEXT NOT NULL,
  city TEXT NOT NULL,
  product_requirements TEXT NOT NULL,
  quantity TEXT NOT NULL,
  budget_range TEXT NOT NULL CHECK (budget_range IN ('under-10k', '10k-50k', '50k-100k', '100k-500k', 'over-500k')),
  requirements TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'cancelled')),
  priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  assigned_to UUID REFERENCES auth.users(id),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 产品表
CREATE TABLE products (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  model TEXT NOT NULL UNIQUE,
  category TEXT NOT NULL,
  description TEXT,
  specifications JSONB DEFAULT '{}',
  price_range TEXT,
  moq TEXT, -- Minimum Order Quantity
  image_url TEXT,
  gallery_images TEXT[] DEFAULT '{}',
  features TEXT[] DEFAULT '{}',
  applications TEXT[] DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 管理员用户表
CREATE TABLE admin_users (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) UNIQUE,
  name TEXT NOT NULL,
  role TEXT DEFAULT 'admin' CHECK (role IN ('admin', 'manager', 'viewer')),
  permissions JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  last_login TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 询价统计表
CREATE TABLE quote_statistics (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  date DATE NOT NULL,
  total_quotes INTEGER DEFAULT 0,
  pending_quotes INTEGER DEFAULT 0,
  completed_quotes INTEGER DEFAULT 0,
  top_countries JSONB DEFAULT '{}',
  top_products JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 系统设置表
CREATE TABLE system_settings (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  key TEXT NOT NULL UNIQUE,
  value JSONB NOT NULL,
  description TEXT,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **2.2 创建索引**
```sql
-- 询价表索引
CREATE INDEX idx_quotes_status ON quotes(status);
CREATE INDEX idx_quotes_created_at ON quotes(created_at DESC);
CREATE INDEX idx_quotes_email ON quotes(email);
CREATE INDEX idx_quotes_company ON quotes(company_name);

-- 产品表索引
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_active ON products(is_active);
CREATE INDEX idx_products_sort ON products(sort_order);
```

### **2.3 设置 Row Level Security (RLS)**
```sql
-- 启用 RLS
ALTER TABLE quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE quote_statistics ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- 询价表策略
CREATE POLICY "Anyone can insert quotes" ON quotes
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can view all quotes" ON quotes
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

CREATE POLICY "Admins can update quotes" ON quotes
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

-- 产品表策略
CREATE POLICY "Anyone can view active products" ON products
  FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage products" ON products
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE user_id = auth.uid() AND is_active = true
    )
  );

-- 管理员表策略
CREATE POLICY "Admins can view admin_users" ON admin_users
  FOR SELECT USING (
    user_id = auth.uid() OR 
    EXISTS (
      SELECT 1 FROM admin_users 
      WHERE user_id = auth.uid() AND role IN ('admin', 'manager')
    )
  );
```

### **2.4 创建触发器**
```sql
-- 更新 updated_at 字段的函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表创建触发器
CREATE TRIGGER update_quotes_updated_at BEFORE UPDATE ON quotes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_users_updated_at BEFORE UPDATE ON admin_users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 📦 **第三步：安装依赖包**

### **3.1 进入 Next.js 项目目录**
```bash
cd karn-materials-shadcn
```

### **3.2 安装 Supabase 相关依赖**
```bash
# 安装 Supabase 客户端
npm install @supabase/supabase-js

# 安装表单处理相关
npm install react-hook-form @hookform/resolvers/zod zod

# 安装 UI 组件
npm install lucide-react sonner

# 安装日期处理
npm install date-fns

# 安装图表库 (用于统计)
npm install recharts

# 安装邮件发送 (可选)
npm install @sendgrid/mail

# 安装开发依赖
npm install -D @types/node
```

### **3.3 安装 Shadcn/ui 组件**
```bash
# 初始化 Shadcn/ui
npx shadcn-ui@latest init

# 安装需要的组件
npx shadcn-ui@latest add button card form input select textarea
npx shadcn-ui@latest add navigation-menu sheet dialog badge
npx shadcn-ui@latest add table tabs alert-dialog
npx shadcn-ui@latest add dropdown-menu avatar
npx shadcn-ui@latest add calendar date-picker
npx shadcn-ui@latest add chart
```

## ⚙️ **第四步：环境配置**

### **4.1 创建环境变量文件**
```bash
# 在项目根目录创建 .env.local
touch .env.local
```

### **4.2 配置环境变量**
```env
# .env.local
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# 邮件配置 (可选)
SENDGRID_API_KEY=your-sendgrid-api-key
ADMIN_EMAIL=<EMAIL>

# 网站配置
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_COMPANY_NAME=杭州卡恩新型建材有限公司
```

### **4.3 更新 .gitignore**
```gitignore
# 添加到 .gitignore
.env.local
.env
```

## 🔌 **第五步：Supabase 客户端配置**

### **5.1 创建 Supabase 客户端**
```typescript
// lib/supabase.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// 服务端客户端 (用于管理员操作)
export const supabaseAdmin = createClient(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)
```

### **5.2 创建类型定义**
```typescript
// types/database.ts
export interface Quote {
  id: string
  business_type: 'bulk' | 'oem' | 'retail' | 'mixed'
  company_name: string
  contact_person: string
  email: string
  phone: string
  country: string
  city: string
  product_requirements: string
  quantity: string
  budget_range: 'under-10k' | '10k-50k' | '50k-100k' | '100k-500k' | 'over-500k'
  requirements?: string
  status: 'pending' | 'processing' | 'completed' | 'cancelled'
  priority: 'low' | 'normal' | 'high' | 'urgent'
  assigned_to?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface Product {
  id: string
  name: string
  model: string
  category: string
  description?: string
  specifications: Record<string, any>
  price_range?: string
  moq?: string
  image_url?: string
  gallery_images: string[]
  features: string[]
  applications: string[]
  is_active: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

export interface AdminUser {
  id: string
  user_id: string
  name: string
  role: 'admin' | 'manager' | 'viewer'
  permissions: Record<string, any>
  is_active: boolean
  last_login?: string
  created_at: string
}
```

## 📝 **第六步：表单集成实现**

### **6.1 创建询价表单组件**
```typescript
// components/forms/QuoteForm.tsx
"use client"

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, CheckCircle, Send } from 'lucide-react'
import { toast } from 'sonner'

const formSchema = z.object({
  businessType: z.enum(['bulk', 'oem', 'retail', 'mixed'], {
    required_error: "请选择业务类型",
  }),
  companyName: z.string().min(2, "公司名称至少2个字符").max(100, "公司名称不能超过100个字符"),
  contactPerson: z.string().min(2, "联系人姓名至少2个字符").max(50, "联系人姓名不能超过50个字符"),
  email: z.string().email("请输入有效的邮箱地址"),
  phone: z.string().min(10, "请输入有效的电话号码").max(20, "电话号码不能超过20位"),
  country: z.string().min(1, "请输入国家").max(50, "国家名称不能超过50个字符"),
  city: z.string().min(1, "请输入城市").max(50, "城市名称不能超过50个字符"),
  productRequirements: z.string().min(1, "请选择产品需求"),
  quantity: z.string().min(1, "请输入预计数量").max(100, "数量描述不能超过100个字符"),
  budgetRange: z.enum(['under-10k', '10k-50k', '50k-100k', '100k-500k', 'over-500k'], {
    required_error: "请选择预算范围",
  }),
  requirements: z.string().max(500, "其他要求不能超过500个字符").optional(),
})

type FormData = z.infer<typeof formSchema>

export function QuoteForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      businessType: undefined,
      companyName: "",
      contactPerson: "",
      email: "",
      phone: "",
      country: "",
      city: "",
      productRequirements: "",
      quantity: "",
      budgetRange: undefined,
      requirements: "",
    },
  })

  async function onSubmit(values: FormData) {
    setIsSubmitting(true)

    try {
      // 提交到 Supabase
      const { data, error } = await supabase
        .from('quotes')
        .insert([
          {
            business_type: values.businessType,
            company_name: values.companyName,
            contact_person: values.contactPerson,
            email: values.email,
            phone: values.phone,
            country: values.country,
            city: values.city,
            product_requirements: values.productRequirements,
            quantity: values.quantity,
            budget_range: values.budgetRange,
            requirements: values.requirements || null,
          }
        ])
        .select()

      if (error) {
        throw error
      }

      // 发送邮件通知 (可选)
      try {
        await fetch('/api/send-notification', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            quoteId: data[0].id,
            type: 'new_quote'
          })
        })
      } catch (emailError) {
        console.warn('Email notification failed:', emailError)
        // 不影响主流程
      }

      setSubmitSuccess(true)
      toast.success("询价提交成功！我们将在24小时内回复您。")
      form.reset()

    } catch (error) {
      console.error('Error submitting quote:', error)
      toast.error("提交失败，请稍后重试。如问题持续，请直接联系我们。")
    } finally {
      setIsSubmitting(false)
    }
  }

  if (submitSuccess) {
    return (
      <Card className="max-w-2xl mx-auto">
        <CardContent className="p-12 text-center">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-4">询价提交成功！</h2>
          <p className="text-gray-600 mb-6">
            感谢您的询价，我们已收到您的需求。我们的专业团队将在24小时内与您联系，为您提供详细的产品方案和报价。
          </p>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-green-800 mb-2">接下来我们将：</h3>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• 专业团队评估您的需求</li>
              <li>• 提供定制化产品方案</li>
              <li>• 发送详细报价和技术资料</li>
              <li>• 安排技术支持和样品寄送</li>
            </ul>
          </div>
          <Button
            onClick={() => setSubmitSuccess(false)}
            className="bg-green-600 hover:bg-green-700"
          >
            提交新的询价
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <div className="flex items-center justify-center mb-4">
          <Badge variant="outline" className="text-green-600 border-green-600">
            <Send className="h-4 w-4 mr-2" />
            在线询价
          </Badge>
        </div>
        <CardTitle className="text-2xl">获取专业报价</CardTitle>
        <CardDescription>
          请填写以下信息，我们将在24小时内回复您的询价。所有信息将严格保密。
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* 业务类型 */}
            <FormField
              control={form.control}
              name="businessType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>业务类型 *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择您的业务类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="bulk">大包装原料 - 批量采购原材料</SelectItem>
                      <SelectItem value="oem">代加工服务 - OEM生产定制</SelectItem>
                      <SelectItem value="retail">小包装半成品 - 零售包装产品</SelectItem>
                      <SelectItem value="mixed">混合业务 - 多种业务需求</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 公司信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="companyName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>公司名称 *</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入公司全称" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contactPerson"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>联系人 *</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入联系人姓名" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 联系方式 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>邮箱 *</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>电话 *</FormLabel>
                    <FormControl>
                      <Input placeholder="+86 138 0000 0000" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 地址信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="country"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>国家 *</FormLabel>
                    <FormControl>
                      <Input placeholder="中国" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="city"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>城市 *</FormLabel>
                    <FormControl>
                      <Input placeholder="杭州" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 产品需求 */}
            <FormField
              control={form.control}
              name="productRequirements"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>产品需求 *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="请选择您需要的产品类型" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="888-standard">888型号 - 标准型墙纸胶粉</SelectItem>
                      <SelectItem value="999-strong">999型号 - 强力型墙纸胶粉</SelectItem>
                      <SelectItem value="777-economy">777型号 - 经济型墙纸胶粉</SelectItem>
                      <SelectItem value="999-plus">999增强型 - 高端墙纸胶粉</SelectItem>
                      <SelectItem value="custom">定制产品 - 根据需求定制</SelectItem>
                      <SelectItem value="multiple">多种产品 - 需要多个型号</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 数量和预算 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>预计数量 *</FormLabel>
                    <FormControl>
                      <Input placeholder="例如：10吨/月 或 500包/次" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="budgetRange"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>预算范围 *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="请选择预算范围" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="under-10k">1万元以下</SelectItem>
                        <SelectItem value="10k-50k">1-5万元</SelectItem>
                        <SelectItem value="50k-100k">5-10万元</SelectItem>
                        <SelectItem value="100k-500k">10-50万元</SelectItem>
                        <SelectItem value="over-500k">50万元以上</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* 其他要求 */}
            <FormField
              control={form.control}
              name="requirements"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>其他要求</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="请描述您的其他要求，如：&#10;• 包装规格要求&#10;• 认证需求（ISO、CE等）&#10;• 标签和品牌要求&#10;• 交货时间要求&#10;• 技术支持需求"
                      className="min-h-[120px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* 提交按钮 */}
            <Button
              type="submit"
              className="w-full bg-green-600 hover:bg-green-700 h-12 text-lg"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  正在提交询价...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-5 w-5" />
                  提交询价
                </>
              )}
            </Button>

            <div className="text-center">
              <p className="text-sm text-gray-500">
                🔒 您的信息将严格保密 • ⏰ 24小时内回复 • 📦 免费样品 • 🎯 专业建议
              </p>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
```
```
