#!/usr/bin/env python3
"""
生成网站占位符图片的脚本
为杭州卡恩新型建材有限公司网站生成专业的占位符图片
"""

import os
from PIL import Image, ImageDraw, ImageFont
import random

def create_placeholder_image(width, height, text, filename, bg_color=None, text_color=None):
    """创建占位符图片"""
    
    # 默认颜色方案 - 使用绿色主题
    if bg_color is None:
        bg_color = (34, 139, 34)  # 森林绿
    if text_color is None:
        text_color = (255, 255, 255)  # 白色
    
    # 创建图片
    img = Image.new('RGB', (width, height), bg_color)
    draw = ImageDraw.Draw(img)
    
    # 尝试使用系统字体
    try:
        # 根据图片大小选择字体大小
        font_size = min(width, height) // 15
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
    except:
        try:
            font_size = min(width, height) // 15
            font = ImageFont.truetype("/System/Library/Fonts/Helvetica.ttc", font_size)
        except:
            font = ImageFont.load_default()
    
    # 计算文本位置（居中）
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    
    # 绘制文本
    draw.text((x, y), text, fill=text_color, font=font)
    
    # 添加装饰边框
    border_width = max(2, min(width, height) // 100)
    draw.rectangle([0, 0, width-1, height-1], outline=(255, 255, 255, 128), width=border_width)
    
    # 保存图片
    img.save(filename, quality=85, optimize=True)
    print(f"✅ 创建: {filename} ({width}x{height})")

def create_logo_placeholder(width, height, filename):
    """创建Logo占位符（PNG格式，透明背景）"""
    
    # 创建透明背景图片
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制圆形背景
    margin = min(width, height) // 10
    circle_bbox = [margin, margin, width-margin, height-margin]
    draw.ellipse(circle_bbox, fill=(34, 139, 34, 255))  # 绿色圆形
    
    # 添加文字
    try:
        font_size = min(width, height) // 8
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
    except:
        font = ImageFont.load_default()
    
    text = "KARN"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    
    draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
    
    # 保存PNG格式
    img.save(filename, format='PNG')
    print(f"✅ 创建Logo: {filename} ({width}x{height})")

def main():
    """主函数 - 生成所有占位符图片"""
    
    print("🎨 开始生成占位符图片...")
    
    # 确保目录存在
    base_dir = "images"
    
    # 1. 产品图片
    products_dir = os.path.join(base_dir, "products")
    create_placeholder_image(800, 800, "888型号\n墙纸胶粉\n产品主图", 
                           os.path.join(products_dir, "product-888-main.jpg"),
                           bg_color=(70, 130, 180))  # 钢蓝色
    
    create_placeholder_image(1200, 800, "产品细节展示\n高品质胶粉\n专业制造", 
                           os.path.join(products_dir, "product-888-detail-1.jpg"),
                           bg_color=(60, 120, 170))
    
    create_placeholder_image(1200, 800, "包装展示\n20kg装\n出口品质", 
                           os.path.join(products_dir, "product-packaging-showcase.jpg"),
                           bg_color=(50, 110, 160))
    
    # 2. 公司介绍图片
    about_dir = os.path.join(base_dir, "about")
    create_placeholder_image(1920, 1080, "现代化生产工厂\n杭州卡恩新型建材\n15年专业制造", 
                           os.path.join(about_dir, "factory-overview.jpg"),
                           bg_color=(34, 139, 34))  # 森林绿
    
    create_placeholder_image(1200, 800, "自动化生产线\n先进设备\n质量保证", 
                           os.path.join(about_dir, "production-line-1.jpg"),
                           bg_color=(46, 125, 50))
    
    create_placeholder_image(1200, 800, "质量控制中心\nISO认证\n严格检测", 
                           os.path.join(about_dir, "quality-control.jpg"),
                           bg_color=(56, 142, 60))
    
    # 3. Logo文件
    logos_dir = os.path.join(base_dir, "logos")
    create_logo_placeholder(512, 512, os.path.join(logos_dir, "karn-logo-main.png"))
    create_logo_placeholder(800, 200, os.path.join(logos_dir, "karn-logo-horizontal.png"))
    
    # 4. 证书图片
    certificates_dir = os.path.join(base_dir, "certificates")
    create_placeholder_image(1200, 1600, "ISO 9001:2015\n质量管理体系认证\n国际标准", 
                           os.path.join(certificates_dir, "iso-9001-certificate.png"),
                           bg_color=(255, 255, 255), text_color=(0, 0, 0))
    
    create_placeholder_image(1200, 1600, "营业执照\n杭州卡恩新型建材\n有限公司", 
                           os.path.join(certificates_dir, "business-license.png"),
                           bg_color=(248, 248, 248), text_color=(0, 0, 0))
    
    # 5. 团队照片
    team_dir = os.path.join(base_dir, "team")
    create_placeholder_image(1920, 1080, "专业团队\n经验丰富\n服务全球", 
                           os.path.join(team_dir, "team-photo-1.jpg"),
                           bg_color=(76, 175, 80))
    
    create_placeholder_image(1200, 800, "工作现场\n专业操作\n品质保证", 
                           os.path.join(team_dir, "working-scene-1.jpg"),
                           bg_color=(66, 165, 70))
    
    # 6. 案例展示
    gallery_dir = os.path.join(base_dir, "gallery")
    create_placeholder_image(1200, 800, "成功案例\n住宅项目\n优质效果", 
                           os.path.join(gallery_dir, "case-study-1.jpg"),
                           bg_color=(33, 150, 243))
    
    create_placeholder_image(1200, 800, "商业应用\n大型项目\n专业施工", 
                           os.path.join(gallery_dir, "application-commercial.jpg"),
                           bg_color=(30, 136, 229))
    
    # 7. 联系页面
    contact_dir = os.path.join(base_dir, "contact")
    create_placeholder_image(1920, 1080, "公司总部\n杭州市\n欢迎来访", 
                           os.path.join(contact_dir, "office-building.jpg"),
                           bg_color=(96, 125, 139))
    
    create_placeholder_image(300, 300, "微信\n二维码\n扫码联系", 
                           os.path.join(contact_dir, "wechat-qr.png"),
                           bg_color=(76, 175, 80))
    
    print("\n🎉 所有占位符图片生成完成！")
    print("📋 生成的图片列表：")
    print("   - 产品图片: 3张")
    print("   - 公司图片: 3张") 
    print("   - Logo文件: 2张")
    print("   - 证书图片: 2张")
    print("   - 团队照片: 2张")
    print("   - 案例展示: 2张")
    print("   - 联系页面: 2张")
    print("   总计: 16张专业占位符图片")

if __name__ == "__main__":
    main()
